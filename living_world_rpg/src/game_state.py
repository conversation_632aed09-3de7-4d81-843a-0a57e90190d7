import logging
import time
from typing import Dict, List, Tuple, Optional, Any

from constants import COLOR_WHITE, COLOR_RED, COLOR_GREEN

class GameState:
    """Holds global state for the game.

    This class manages all shared state across different game screens and systems.
    It includes game settings, player state, and UI feedback mechanisms.
    """

    def __init__(self) -> None:
        """Initialize the game state with default values."""
        try:
            # Game state
            self.current_state: str = "start_menu"
            self.started_game: bool = False
            self.is_fullscreen: bool = False
            self.debug_overlay: bool = False

            # World generation
            self.selected_seed: str = ""
            self.preview_map: Optional[Dict[str, Any]] = None
            self.player_biome_name: Optional[str] = None
            self.selected_biome_index: int = 0

            # Player state
            self.player_race_name: Optional[str] = None
            self.selected_race_index: int = 0
            self.player_x: float = 0.0
            self.player_y: float = 0.0
            self.companion_x: float = 0.0
            self.companion_y: float = 1.0
            self.follow_mode: bool = False
            self.last_attack_time: float = 0.0

            # Inventory and resources
            self.inventory_open: bool = False
            self.npc_ore_count: int = 0
            self.last_ore_gather: float = time.time()

            # UI feedback
            self.feedback_messages: list[tuple[str, float, float, tuple[int, int, int]]] = []
            self.message_duration: float = 2.0  # Message display time in seconds
            self.default_message_color: tuple[int, int, int] = COLOR_WHITE
            self.error_message_color: tuple[int, int, int] = COLOR_RED
            self.success_message_color: tuple[int, int, int] = COLOR_GREEN

        except Exception as e:
            logging.error(f"Error initializing GameState: {str(e)}")
            raise

    def show_feedback_message(self, 
                            message: str, 
                            color: tuple[int, int, int] = None,
                            duration: float = None) -> None:
        """Add a new feedback message that will be displayed on the gameplay screen.

        Args:
            message (str): The text message to display
            color (tuple[int, int, int], optional): RGB tuple for the message color.
                Defaults to default message color.
            duration (float, optional): Duration in seconds to display the message.
                Defaults to message_duration.
        """
        try:
            color = color or self.default_message_color
            duration = duration or self.message_duration
            current_time = time.time()
            self.feedback_messages.append((message, current_time, current_time + duration, color))
        except Exception as e:
            logging.error(f"Error adding feedback message: {str(e)}")
            raise

    def show_error_message(self, message: str) -> None:
        """Show an error message with red color."""
        self.show_feedback_message(message, color=self.error_message_color)

    def show_success_message(self, message: str) -> None:
        """Show a success message with green color."""
        self.show_feedback_message(message, color=self.success_message_color)

from constants import COLOR_GREEN, COLOR_RED


class GameState:
    """Manages the game's state and provides feedback to the player."""
    
    def __init__(self):
        """Initialize the game state."""
        self.feedback_messages: List[str] = []
        self.success_color = COLOR_GREEN
        self.error_color = COLOR_RED
        self.current_screen: Optional[str] = None
        self.is_paused = False
        
    def show_feedback_message(self, message: str, is_success: bool = True) -> None:
        """Show a feedback message to the player.
        
        Args:
            message: The message to display.
            is_success: Whether this is a success message (True) or an error message (False).
        """
        color = self.success_color if is_success else self.error_color
        self.feedback_messages.append(f"{message}")
        logging.info(f"Feedback: {message}")
        
    def clear_feedback_messages(self) -> None:
        """Clear all feedback messages."""
        self.feedback_messages.clear()
