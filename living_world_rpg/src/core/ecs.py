# File: src/core/ecs.py
"""
Core ECS framework for Living World RPG.
Provides the Entity class, the abstract System class, and the ECSWorld manager.
"""

# Standard library imports
import logging
import uuid
from typing import Any, Dict, List, Optional, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
from utils.elastic_dict_adapter import ElasticDictAdapter

# Configure logging
logging.basicConfig(level=logging.INFO)


class Entity:
    """Represents a game entity with a unique ID and a collection of components."""
    def __init__(self) -> None:
        self.id = uuid.uuid4()
        self.components = ElasticDictAdapter(size=16)  # Most entities have few components

    def add_component(self, component: Any) -> None:
        """
        Attaches a component to the entity.
        Components are stored by their class type.
        """
        self.components[type(component)] = component

    def get_component(self, comp_type: Type) -> Any:
        """
        Retrieves the component of the given type, if present.
        """
        return self.components.get(comp_type)

    def remove_component(self, comp_type: Type) -> None:
        """Removes the component of the given type."""
        self.components.pop(comp_type, None)


class System:
    """
    Base class for all systems.
    Subclasses must implement the update() method.
    """
    def __init__(self) -> None:
        self.entities: List[Entity] = []
        self.world: "ECSWorld" = None  # Will be set when the system is added to the world

    def required_components(self) -> List[Type]:
        """
        Returns a list of component types required for an entity to be processed by this system.
        """
        return []

    def register_entity(self, entity: Entity) -> None:
        """
        Registers an entity if it contains all required components.
        """
        if all(entity.get_component(comp) is not None for comp in self.required_components()):
            self.entities.append(entity)

    def unregister_entity(self, entity: Entity) -> None:
        """Unregisters an entity from this system."""
        if entity in self.entities:
            self.entities.remove(entity)

    def update(self, dt: float, **kwargs) -> None:
        """Processes all entities. Must be overridden by subclasses."""
        raise NotImplementedError("System must implement update(dt, **kwargs)")


class ECSWorld:
    """
    Manages all entities and systems.
    Also tracks global game time.
    """
    def __init__(self) -> None:
        self.entities: List[Entity] = []
        self.systems: List[System] = []
        self.day_time: float = 0.0  # In-game time (e.g., hours)
        self.game_days: int = 0     # Total number of days elapsed

    def create_entity(self) -> Entity:
        """Creates and registers a new entity."""
        entity = Entity()
        self.entities.append(entity)
        for system in self.systems:
            system.register_entity(entity)
        return entity

    def add_system(self, system: System) -> None:
        """Adds a system and registers all existing entities with it."""
        system.world = self
        self.systems.append(system)
        for entity in self.entities:
            system.register_entity(entity)

    def update(self, dt: float, **kwargs) -> None:
        """
        Advances the world by dt and updates all systems.
        Also increments game days based on day_time.
        """
        self.day_time += dt
        if self.day_time >= 24.0:
            self.day_time -= 24.0
            self.game_days += 1
        for system in self.systems:
            system.update(dt, **kwargs)

    def get_entities_with(self, *component_types) -> List[Any]:
        """
        Returns a list of tuples (entity, components_tuple) for entities that have all the specified components.
        """
        results = []
        for entity in self.entities:
            comps = [entity.get_component(ct) for ct in component_types]
            if all(comps):
                results.append((entity, tuple(comps)))
        return results

    def get_player_entity(self) -> Any:
        """
        Returns the first entity that has a PlayerComponent.
        """
        from core.components import PlayerComponent
        for entity in self.entities:
            if entity.get_component(PlayerComponent):
                return entity
        return None

    @property
    def total_days(self) -> float:
        """Returns the total number of days elapsed, including fractional days."""
        return self.game_days + (self.day_time / 24.0)
