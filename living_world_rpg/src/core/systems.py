# File: src/core/systems.py
"""
Core systems for Living World RPG.
Includes systems for resource production/consumption, faction behavior,
economy, camera, combat, leveling, crafting, status effects, and gathering.
"""

# Standard library imports
import logging
import math
import random
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar, Union, Callable

# Third-party imports
import numpy as np

# Local application imports
from core.ecs import System
from core.components import (
    AIComponent, CameraComponent, CombatComponent, CreatureComponent,
    GatheringSkillComponent, InventoryComponent, PositionComponent,
    SettlementComponent, StatsComponent, ToolComponent
)
from world.world_manager import WorldManager

# Configure logging
logging.basicConfig(level=logging.INFO)


# -------------------------------
# Resource Production & Consumption Systems
# -------------------------------

class ResourceProductionSystem(System):
    """
    Increases settlement inventories based on production rates,
    modified by seasonal and weather factors.
    """
    def __init__(self, biome_data, seasonal_data, resource_manager, faction_data, special_conditions, event_thresholds, weather_data):
        super().__init__()
        self.biome_data = biome_data
        self.seasonal_data = seasonal_data
        self.resource_manager = resource_manager
        self.faction_data = faction_data
        self.special_conditions = special_conditions
        self.event_thresholds = event_thresholds
        self.weather_data = weather_data

    def required_components(self):
        return [SettlementComponent, InventoryComponent]

    def update(self, dt: float, current_season=None, current_weather="Clear", **kwargs):
        # Default season data
        season_data = {}
        
        # Handle different types of current_season
        if current_season is None:
            season_name = 'Spring'
            season_data = self.seasonal_data.get(season_name, {})
        elif isinstance(current_season, dict):
            # If it's a dict, try to get the season name
            season_name = current_season.get('name', 'Spring')
            season_data = self.seasonal_data.get(season_name, {})
        elif isinstance(current_season, str):
            # If it's a string, use it as the season name
            season_name = current_season
            season_data = self.seasonal_data.get(season_name, {})
        else:
            # Fallback to Spring for any other type
            season_name = 'Spring'
            season_data = self.seasonal_data.get(season_name, {})
        
        # Get the season factor, defaulting to 1.0 if not found
        season_factor = 1.0
        if isinstance(season_data, dict):
            season_factor = season_data.get("modifier", 1.0)
        weather_mods = self.weather_data.get(current_weather, {}).get("resource_modifiers", {})
        from ..core.components import SettlementComponent, InventoryComponent
        for entity in self.entities:
            settlement = entity.get_component(SettlementComponent)
            inventory = entity.get_component(InventoryComponent)
            if not settlement or not inventory:
                continue
            for resource, base_amt in settlement.production_rate.items():
                w_mod = weather_mods.get(resource.lower(), 1.0)
                final_amt = int(base_amt * season_factor * w_mod)
                inventory.items[resource] = inventory.items.get(resource, 0) + final_amt
            logging.info(f"[ResourceProductionSystem] Updated production for settlement '{settlement.name}'.")


class ResourceConsumptionSystem(System):
    """
    Decreases settlement inventories based on consumption rates.
    """
    def __init__(self, resource_manager):
        super().__init__()
        self.resource_manager = resource_manager

    def required_components(self):
        return [SettlementComponent, InventoryComponent]

    def update(self, dt: float, **kwargs):
        for entity in self.entities:
            settlement = entity.get_component(SettlementComponent)
            inventory = entity.get_component(InventoryComponent)
            if not settlement or not inventory:
                continue
            for resource, needed in settlement.consumption_rate.items():
                current_qty = inventory.items.get(resource, 0)
                if current_qty >= needed:
                    inventory.items[resource] -= needed
                else:
                    inventory.items[resource] = 0
                    logging.warning(f"[ResourceConsumptionSystem] Settlement '{settlement.name}' is short on {resource}!")


# -------------------------------
# AI, Faction and Economy Systems
# -------------------------------

class AISystem(System):
    """
    Processes AI components for NPCs, handling behavior trees, GOAP planning,
    scheduling, emotional modeling, and social relationships.
    """
    def required_components(self):
        from ..core.components import AIComponent, PositionComponent
        return [AIComponent, PositionComponent]

    def update(self, dt: float, **kwargs):
        from ..core.components import AIComponent, PositionComponent
        from ..ai.behavior_tree import BehaviorTree, Blackboard, NodeStatus
        from ..ai.goap import GoapAgent, WorldState, Goal, Action
        from ..ai.scheduling import ScheduleManager, TimeOfDay, WeatherType, EventType
        from ..ai.social import EmotionalState, SocialNetwork, SocialEventManager
        from ai.scheduling import ScheduleManager, TimeOfDay, WeatherType, EventType
        from ai.social import EmotionalState, SocialNetwork, SocialEventManager

        # Get game state and world info
        game_state = kwargs.get('game_state', None)
        if not game_state:
            return

        # Get current time, weather, and events
        current_hour = getattr(game_state, 'current_hour', 12)
        current_weather = getattr(game_state, 'current_weather', 'clear')
        current_event = getattr(game_state, 'current_event', 'normal')

        # Process each AI entity
        for entity in self.entities:
            ai = entity.get_component(AIComponent)
            position = entity.get_component(PositionComponent)

            if not ai or not position:
                continue

            # Update AI based on behavior type
            if ai.behavior_type == 'behavior_tree':
                self._process_behavior_tree(entity, ai, position, dt, game_state)
            elif ai.behavior_type == 'goap':
                self._process_goap(entity, ai, position, dt, game_state)
            else:
                # Basic AI - just log the tick
                logging.debug(f"[AISystem] Processing basic AI for entity {entity.id}")

    def _process_behavior_tree(self, entity, ai, position, dt, game_state):
        """Process an entity using behavior tree AI."""
        # Update blackboard with current entity state
        ai.blackboard['entity'] = entity
        ai.blackboard['position'] = (position.x, position.y)
        ai.blackboard['dt'] = dt
        ai.blackboard['game_state'] = game_state

        # If the entity has a behavior tree, tick it
        if hasattr(ai, 'behavior_tree') and ai.behavior_tree:
            status = ai.behavior_tree.tick()
            if status == NodeStatus.SUCCESS:
                logging.debug(f"[AISystem] Entity {entity.id} behavior tree completed successfully")
            elif status == NodeStatus.FAILURE:
                logging.debug(f"[AISystem] Entity {entity.id} behavior tree failed")
        else:
            logging.warning(f"[AISystem] Entity {entity.id} has behavior_tree type but no tree")

    def _process_goap(self, entity, ai, position, dt, game_state):
        """Process an entity using GOAP AI."""
        # Update world state with current entity state
        if not hasattr(ai, 'goap_agent') or not ai.goap_agent:
            logging.warning(f"[AISystem] Entity {entity.id} has goap type but no agent")
            return

        # Update the agent's world state
        ai.goap_agent.update_world_state('entity_id', entity.id)
        ai.goap_agent.update_world_state('position_x', position.x)
        ai.goap_agent.update_world_state('position_y', position.y)

        # Think about what to do next
        action = ai.goap_agent.think()
        if action:
            # Execute the action
            success = ai.goap_agent.execute_action(action)
            if success:
                logging.debug(f"[AISystem] Entity {entity.id} executed action {action.name}")
            else:
                logging.warning(f"[AISystem] Entity {entity.id} failed to execute action {action.name}")
        else:
            logging.debug(f"[AISystem] Entity {entity.id} has no action to take")

class FactionBehaviorSystem(System):
    """
    Processes faction AI; currently logs behavior ticks.
    """
    def required_components(self):
        from core.components import FactionComponent, RelationshipComponent
        return [FactionComponent, RelationshipComponent]

    def update(self, dt: float, **kwargs):
        from core.components import FactionComponent, RelationshipComponent
        for entity in self.entities:
            faction = entity.get_component(FactionComponent)
            relations = entity.get_component(RelationshipComponent)
            if faction and relations:
                logging.info(f"[FactionBehaviorSystem] Processing faction '{faction.faction_name}'.")


class EconomySystem(System):
    """
    Logs economy ticks for settlements.
    Integrates dynamic pricing via EconomyManager.
    """
    def __init__(self, items_data, economy_manager):
        super().__init__()
        self.items_data = items_data
        self.economy_manager = economy_manager

    def required_components(self):
        from core.components import SettlementComponent, InventoryComponent
        return [SettlementComponent, InventoryComponent]

    def update(self, dt: float, **kwargs):
        from core.components import SettlementComponent, InventoryComponent
        # Calculate dynamic prices based on current supply and demand
        settlements = self.world.get_entities_with(SettlementComponent, InventoryComponent)
        self.economy_manager.calculate_prices(settlements)
        # Log updated dynamic prices at debug level
        logging.debug(f"[EconomySystem] Updated dynamic prices: {self.economy_manager.dynamic_prices}")


# -------------------------------
# Camera System
# -------------------------------

class CameraSystem(System):
    """
    Manages the camera position with smooth following and chunk loading.
    """
    def __init__(self, world_width, world_height):
        super().__init__()
        self.world_width = world_width
        self.world_height = world_height
        self.smooth_speed = 5.0  # Lower is smoother, higher is snappier
        self.last_camera_pos = None

    def required_components(self):
        from .components import CameraComponent, PositionComponent
        return [CameraComponent, PositionComponent]

    def update(self, dt: float, **kwargs):
        from core.components import PositionComponent, CameraComponent
        
        # Get player and camera components
        player = self.world.get_player_entity()
        if not player:
            return
            
        ppos = player.get_component(PositionComponent)
        if not ppos:
            return
            
        for entity in self.entities:
            cam_comp = entity.get_component(CameraComponent)
            if not cam_comp:
                continue
                
            # Calculate target position (centered on player)
            target_x = max(0, min(self.world_width - cam_comp.width, ppos.x - cam_comp.width / 2))
            target_y = max(0, min(self.world_height - cam_comp.height, ppos.y - cam_comp.height / 2))
            
            # Initialize last_camera_pos if needed
            if self.last_camera_pos is None:
                self.last_camera_pos = (target_x, target_y)
                
            # Smoothly interpolate camera position
            last_x, last_y = self.last_camera_pos
            smooth_factor = 1.0 - (1.0 / (1.0 + self.smooth_speed * dt))
            
            new_x = last_x + (target_x - last_x) * smooth_factor
            new_y = last_y + (target_y - last_y) * smooth_factor
            
            # Update camera component
            cam_comp.x = new_x
            cam_comp.y = new_y
            self.last_camera_pos = (new_x, new_y)
            
            # Update world manager with player position for chunk loading
            world_manager = self.world.get_system(WorldManager)
            if world_manager:
                world_manager.update_active_regions(ppos.x, ppos.y)
            
            logging.debug(f"[CameraSystem] Camera at ({cam_comp.x:.1f}, {cam_comp.y:.1f}) "
                        f"targeting ({target_x:.1f}, {target_y:.1f})")
                        
            # Add a small border around the camera to preload chunks
            camera_border = 2  # in tiles
            cam_left = int((cam_comp.x - camera_border) // 32)
            cam_top = int((cam_comp.y - camera_border) // 32)
            cam_right = int((cam_comp.x + cam_comp.width + camera_border) // 32)
            cam_bottom = int((cam_comp.y + cam_comp.height + camera_border) // 32)
            
            # This will ensure chunks around the camera are loaded
            if world_manager and hasattr(world_manager, 'update_active_regions'):
                # Check the four corners of the camera view plus a small border
                world_manager.update_active_regions(ppos.x, ppos.y)


# -------------------------------
# Combat System
# -------------------------------

class CombatSystem(System):
    """
    Handles combat interactions.
    Provides a method for the player to initiate an attack on adjacent hostile entities.
    """
    def required_components(self):
        from core.components import PositionComponent, CombatComponent, CreatureComponent
        return [PositionComponent, CombatComponent, CreatureComponent]

    def player_initiate_attack(self, dt: float = 0, **kwargs):
        from core.components import PositionComponent, CombatComponent, CreatureComponent, StatsComponent
        player = self.world.get_player_entity()
        if not player:
            logging.info("[CombatSystem] No player found.")
            return
        ppos = player.get_component(PositionComponent)
        pcombat = player.get_component(CombatComponent)
        pstats = player.get_component(StatsComponent)
        if not (ppos and pcombat and pstats):
            logging.info("[CombatSystem] Player missing components.")
            return
        px, py = ppos.x, ppos.y
        targets = []
        for entity in self.entities:
            pos_c = entity.get_component(PositionComponent)
            ccombat = entity.get_component(CombatComponent)
            creature_c = entity.get_component(CreatureComponent)
            if ccombat and creature_c and ccombat.is_hostile:
                if abs(px - pos_c.x) + abs(py - pos_c.y) == 1:
                    targets.append((entity, pos_c, ccombat, creature_c))
        if not targets:
            logging.info("[CombatSystem] No adjacent hostile target.")
            return
        target, tpos, tcombat, tcreature = targets[0]
        damage = max(0, pcombat.attack - tcombat.defense)
        old_hp = tcreature.hit_points
        tcreature.hit_points = max(0, old_hp - damage)
        logging.info(f"[CombatSystem] Player dealt {damage} damage to {tcreature.creature_name} (HP: {old_hp}->{tcreature.hit_points}).")
        if tcreature.hit_points <= 0:
            logging.warning(f"[CombatSystem] {tcreature.creature_name} was slain!")
            if hasattr(self.world, "delete_entity"):
                self.world.delete_entity(target)

    def update(self, dt: float, **kwargs):
        from core.components import PositionComponent, CombatComponent, CreatureComponent, StatsComponent
        player = self.world.get_player_entity()
        if not player:
            return
        ppos = player.get_component(PositionComponent)
        pstats = player.get_component(StatsComponent)
        if not (ppos and pstats):
            return
        for entity in self.entities:
            pos_c = entity.get_component(PositionComponent)
            ccombat = entity.get_component(CombatComponent)
            creature_c = entity.get_component(CreatureComponent)
            if pos_c and ccombat and creature_c and ccombat.is_hostile:
                if abs(ppos.x - pos_c.x) + abs(ppos.y - pos_c.y) == 1:
                    dmg = max(0, ccombat.attack - 5)  # Fixed player defense for simplicity
                    pstats.hp = max(0, pstats.hp - dmg)
                    logging.info(f"[CombatSystem] {creature_c.creature_name} hit the player for {dmg} damage.")
                    if pstats.hp <= 0:
                        logging.warning("[CombatSystem] Player has been defeated!")


# -------------------------------
# Leveling System
# -------------------------------

class LevelingSystem(System):
    """
    Processes leveling for entities.
    Entities with a LevelingComponent and StatsComponent will level up when XP exceeds the threshold.
    """
    def required_components(self):
        from core.components import LevelingComponent, StatsComponent
        return [LevelingComponent, StatsComponent]

    def update(self, dt: float, **kwargs):
        from core.components import LevelingComponent, StatsComponent
        for entity in self.entities:
            level_comp = entity.get_component(LevelingComponent)
            stats_comp = entity.get_component(StatsComponent)
            if not level_comp or not stats_comp:
                continue
            while level_comp.xp >= level_comp.xp_threshold:
                level_comp.level += 1
                level_comp.xp -= level_comp.xp_threshold
                level_comp.xp_threshold = int(level_comp.xp_threshold * 1.2)
                stats_comp.max_hp += 10
                stats_comp.max_mp += 5
                stats_comp.hp = stats_comp.max_hp
                stats_comp.mp = stats_comp.max_mp
                logging.info(f"[LevelingSystem] Entity {entity.id} reached level {level_comp.level}.")


# -------------------------------
# Crafting System
# -------------------------------

class CraftingSystem(System):
    """
    Processes crafting requests for entities.
    Entities with InventoryComponent and CraftingRequestComponent will attempt to craft
    the requested recipe if ingredients are available.
    """
    def required_components(self):
        from core.components import InventoryComponent, CraftingRequestComponent
        return [InventoryComponent, CraftingRequestComponent]

    def update(self, dt: float, **kwargs):
        from core.components import InventoryComponent, CraftingRequestComponent
        for entity in self.entities:
            inventory = entity.get_component(InventoryComponent)
            craft_req = entity.get_component(CraftingRequestComponent)
            if not inventory or not craft_req or not craft_req.recipe:
                continue
            recipe = craft_req.recipe
            if all(inventory.items.get(item, 0) >= qty for item, qty in recipe.get("ingredients", {}).items()):
                for item, qty in recipe["ingredients"].items():
                    inventory.items[item] -= qty
                output_item = recipe.get("output")
                output_qty = recipe.get("output_qty", 1)
                inventory.items[output_item] = inventory.items.get(output_item, 0) + output_qty
                logging.info(f"[CraftingSystem] Crafted {output_item} (x{output_qty}) for entity {entity.id}.")
                craft_req.recipe = None  # Clear request after crafting


# -------------------------------
# Movement System
# -------------------------------

class MovementSystem(System):
    """
    Handles movement for creatures and NPCs.
    Implements different movement patterns based on creature behavior.
    """
    def __init__(self):
        super().__init__()
        self.movement_patterns = {
            "Neutral": self._neutral_movement,
            "Aggressive": self._aggressive_movement,
            "Passive": self._passive_movement,
            "Friendly": self._friendly_movement
        }
        self.movement_timers = {}  # Entity ID -> next movement time
        self.movement_cooldown = 1.0  # Base time between movements
        self.movement_range = 5.0  # How far creatures can detect the player
        self.movement_speed = 1.0  # Base movement speed

    def required_components(self):
        from core.components import PositionComponent, CreatureComponent
        return [PositionComponent, CreatureComponent]

    def update(self, dt: float, **kwargs):
        from core.components import PositionComponent, CreatureComponent

        # Get player entity and position
        player = self.world.get_player_entity()
        player_pos = None
        if player:
            player_pos = player.get_component(PositionComponent)

        current_time = kwargs.get('current_time', 0)

        # Process each creature
        for entity in self.entities:
            entity_id = entity.id

            # Skip if on cooldown
            if entity_id in self.movement_timers and current_time < self.movement_timers[entity_id]:
                continue

            pos_comp = entity.get_component(PositionComponent)
            creature_comp = entity.get_component(CreatureComponent)

            if not pos_comp or not creature_comp:
                continue

            # Get the appropriate movement pattern based on behavior
            behavior = creature_comp.behavior
            movement_func = self.movement_patterns.get(behavior, self._neutral_movement)

            # Apply the movement pattern
            moved = movement_func(entity, pos_comp, creature_comp, player_pos, dt)

            if moved:
                # Set cooldown for next movement
                cooldown_modifier = 1.0
                if behavior == "Aggressive":
                    cooldown_modifier = 0.7  # Aggressive creatures move faster
                elif behavior == "Passive":
                    cooldown_modifier = 1.5  # Passive creatures move slower

                self.movement_timers[entity_id] = current_time + (self.movement_cooldown * cooldown_modifier)
                logging.debug(f"[MovementSystem] {creature_comp.creature_name} moved to ({pos_comp.x:.1f}, {pos_comp.y:.1f})")

    def _neutral_movement(self, entity, pos_comp, creature_comp, player_pos, dt):
        """Random wandering movement pattern"""
        # 30% chance to move in a random direction
        if random.random() < 0.3:
            direction = random.choice([(0, 1), (1, 0), (0, -1), (-1, 0)])
            pos_comp.x += direction[0] * self.movement_speed
            pos_comp.y += direction[1] * self.movement_speed
            return True
        return False

    def _aggressive_movement(self, entity, pos_comp, creature_comp, player_pos, dt):
        """Move toward the player if in range, otherwise wander"""
        if player_pos and self._is_in_range(pos_comp, player_pos, self.movement_range):
            # Move toward player
            dx = 1 if player_pos.x > pos_comp.x else (-1 if player_pos.x < pos_comp.x else 0)
            dy = 1 if player_pos.y > pos_comp.y else (-1 if player_pos.y < pos_comp.y else 0)

            # Normalize diagonal movement
            if dx != 0 and dy != 0:
                dx *= 0.707  # 1/√2
                dy *= 0.707

            pos_comp.x += dx * self.movement_speed * dt * 2  # Move faster when chasing
            pos_comp.y += dy * self.movement_speed * dt * 2
            return True
        else:
            # Random movement when player not in range
            return self._neutral_movement(entity, pos_comp, creature_comp, player_pos, dt)

    def _passive_movement(self, entity, pos_comp, creature_comp, player_pos, dt):
        """Move away from the player if in range, otherwise rarely wander"""
        if player_pos and self._is_in_range(pos_comp, player_pos, self.movement_range * 0.7):
            # Move away from player
            dx = -1 if player_pos.x > pos_comp.x else (1 if player_pos.x < pos_comp.x else 0)
            dy = -1 if player_pos.y > pos_comp.y else (1 if player_pos.y < pos_comp.y else 0)

            # Normalize diagonal movement
            if dx != 0 and dy != 0:
                dx *= 0.707  # 1/√2
                dy *= 0.707

            pos_comp.x += dx * self.movement_speed * dt * 1.5  # Move a bit faster when fleeing
            pos_comp.y += dy * self.movement_speed * dt * 1.5
            return True
        elif random.random() < 0.15:  # Less likely to move randomly
            direction = random.choice([(0, 1), (1, 0), (0, -1), (-1, 0)])
            pos_comp.x += direction[0] * self.movement_speed * 0.5  # Move slower
            pos_comp.y += direction[1] * self.movement_speed * 0.5
            return True
        return False

    def _friendly_movement(self, entity, pos_comp, creature_comp, player_pos, dt):
        """Move toward the player if in medium range, stay close but not too close"""
        if player_pos:
            distance = self._get_distance(pos_comp, player_pos)

            if distance > self.movement_range * 0.8:
                # Too far, move toward player
                dx = 1 if player_pos.x > pos_comp.x else (-1 if player_pos.x < pos_comp.x else 0)
                dy = 1 if player_pos.y > pos_comp.y else (-1 if player_pos.y < pos_comp.y else 0)

                # Normalize diagonal movement
                if dx != 0 and dy != 0:
                    dx *= 0.707  # 1/√2
                    dy *= 0.707

                pos_comp.x += dx * self.movement_speed * dt
                pos_comp.y += dy * self.movement_speed * dt
                return True
            elif distance < 2.0:
                # Too close, back up a bit
                dx = -1 if player_pos.x > pos_comp.x else (1 if player_pos.x < pos_comp.x else 0)
                dy = -1 if player_pos.y > pos_comp.y else (1 if player_pos.y < pos_comp.y else 0)

                # Normalize diagonal movement
                if dx != 0 and dy != 0:
                    dx *= 0.707  # 1/√2
                    dy *= 0.707

                pos_comp.x += dx * self.movement_speed * dt * 0.5
                pos_comp.y += dy * self.movement_speed * dt * 0.5
                return True
            elif random.random() < 0.2:
                # Random small movements when at good distance
                direction = random.choice([(0, 0.5), (0.5, 0), (0, -0.5), (-0.5, 0)])
                pos_comp.x += direction[0] * self.movement_speed * dt
                pos_comp.y += direction[1] * self.movement_speed * dt
                return True
        else:
            # No player, just wander occasionally
            return self._neutral_movement(entity, pos_comp, creature_comp, player_pos, dt)
        return False

    def _is_in_range(self, pos1, pos2, range_val):
        """Check if two positions are within a certain range of each other"""
        return self._get_distance(pos1, pos2) <= range_val

    def _get_distance(self, pos1, pos2):
        """Calculate the Euclidean distance between two positions"""
        return math.sqrt((pos1.x - pos2.x) ** 2 + (pos1.y - pos2.y) ** 2)


# -------------------------------
# Status Effect System
# -------------------------------

class StatusEffectSystem(System):
    """
    Updates status effects on entities.
    Decreases the remaining duration and removes expired effects.
    """
    def required_components(self):
        from core.components import StatusEffectComponent
        return [StatusEffectComponent]

    def update(self, dt: float, **kwargs):
        from core.components import StatusEffectComponent
        for entity in self.entities:
            status_comp = entity.get_component(StatusEffectComponent)
            if not status_comp:
                continue
            active_effects = []
            for effect in status_comp.effects:
                effect.remaining -= dt
                if effect.remaining > 0:
                    active_effects.append(effect)
                else:
                    logging.info(f"[StatusEffectSystem] Effect '{effect.name}' expired on entity {entity.id}.")
            status_comp.effects = active_effects


# -------------------------------
# Gathering System
# -------------------------------

class GatheringResult:
    """Holds the result of a gathering attempt"""
    def __init__(self, success: bool, message: str,
                 resource_gained: int = 0, tool_broke: bool = False):
        self.success = success
        self.message = message
        self.resource_gained = resource_gained
        self.tool_broke = tool_broke

class GatheringSystem(System):
    """Handles resource gathering mechanics"""
    def __init__(self):
        super().__init__()
        self.gathering_cooldown = 1.0
        self.last_gather_time = 0.0

        # Tool requirements and base damage per use
        self.tool_requirements = {
            "wood": {"tool": "axe", "skill": "woodcutting", "damage": 1.0},
            "iron_ore": {"tool": "pickaxe", "skill": "mining", "damage": 2.0},
            "herbs": {"tool": None, "skill": "herbalism", "damage": 0},
            "stone": {"tool": "pickaxe", "skill": "mining", "damage": 1.5},
            "rare_crystals": {"tool": "pickaxe", "skill": "mining", "damage": 3.0},
            "grain": {"tool": "sickle", "skill": "farming", "damage": 0.5}
        }

        self.gathering_xp = {
            "common": 5,
            "uncommon": 10,
            "rare": 20,
            "legendary": 50
        }

    def required_components(self):
        from .components import InventoryComponent, GatheringSkillComponent, ToolComponent
        return [InventoryComponent, GatheringSkillComponent, ToolComponent]

    def can_gather_resource(self, tool_comp: ToolComponent, skill_comp: GatheringSkillComponent,
                          resource_name: str, resource_data: dict) -> tuple[bool, str]:
        """Check if gathering is possible and return (can_gather, reason)"""
        requirements = self.tool_requirements.get(resource_name, {})
        required_tool = requirements.get("tool")

        if required_tool:
            tool = tool_comp.get_tool(required_tool)
            if not tool:
                return False, f"Need a {required_tool} to gather {resource_name}"
            if tool["durability"] <= 0:
                return False, f"Your {required_tool} is broken and needs repair"

        required_skill = requirements.get("skill")
        if required_skill:
            min_skill_level = resource_data.get("min_skill_level", 1)
            current_level = skill_comp.skills.get(required_skill, 0)
            if current_level < min_skill_level:
                return False, f"Need {required_skill} level {min_skill_level} (current: {current_level})"

        return True, "OK"

    def gather_resource(self, entity, resource_name: str, resource_data: dict,
                       current_time: float) -> GatheringResult:
        """Attempt to gather a resource, considering tools, skills, and cooldown"""
        if current_time - self.last_gather_time < self.gathering_cooldown:
            return GatheringResult(False, "Gathering too fast", 0, False)

        inv_comp = entity.get_component(InventoryComponent)
        tool_comp = entity.get_component(ToolComponent)
        skill_comp = entity.get_component(GatheringSkillComponent)

        can_gather, reason = self.can_gather_resource(tool_comp, skill_comp,
                                                    resource_name, resource_data)
        if not can_gather:
            return GatheringResult(False, reason, 0, False)

        # Get tool effectiveness and apply damage
        requirements = self.tool_requirements.get(resource_name, {})
        required_tool = requirements.get("tool")
        tool_broke = False

        if required_tool:
            effectiveness = tool_comp.get_tool_effectiveness(required_tool)
            if effectiveness <= 0:
                return GatheringResult(False, f"{required_tool} is not effective", 0, False)

            # Apply tool damage
            base_damage = requirements.get("damage", 1.0)
            tool_broke = tool_comp.damage_tool(required_tool, base_damage)

        # Calculate gather amount based on skill bonus and tool effectiveness
        skill_type = requirements.get("skill", "")
        skill_bonus = skill_comp.get_gathering_bonus(skill_type)
        base_amount = resource_data.get("quantity", 1)
        tool_factor = tool_comp.get_tool_effectiveness(required_tool) if required_tool else 1.0
        gather_amount = max(1, int(base_amount * skill_bonus * tool_factor * 0.2))

        # Award XP based on resource rarity
        rarity = resource_data.get("rarity", "common").lower()
        xp_award = self.gathering_xp.get(rarity, 5)
        skill_comp.gain_xp(skill_type, xp_award)

        # Update inventory
        if resource_name not in inv_comp.items:
            inv_comp.items[resource_name] = 0
        inv_comp.items[resource_name] += gather_amount

        # Update last gather time
        self.last_gather_time = current_time

        msg = f"Gathered {gather_amount} {resource_name}"
        if tool_broke:
            msg += f". Your {required_tool} broke!"

        return GatheringResult(True, msg, gather_amount, tool_broke)

    def update(self, dt: float, **kwargs):
        """Update gathering cooldowns and process any automated gathering"""
        pass  # Main gathering is handled through direct calls to gather_resource
