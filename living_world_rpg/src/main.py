# File: src/main.py

# Standard library imports
import hashlib
import logging
import math
import os
import random
import sys
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

# Third-party imports
import numpy as np
import pygame
from pygame.locals import *

# Local application imports
from constants import TEXT_FONT_SIZE, WINDOW_HEIGHT, WINDOW_WIDTH
from controls import get_control_from_event, is_control_pressed
from core.components import (
    AIComponent, CameraComponent, CombatComponent, CreatureComponent,
    FactionComponent, GatheringSkillComponent, InventoryComponent, LevelingComponent,
    PlayerComponent, PositionComponent, SettlementComponent, StatsComponent,
    StatusEffectComponent, ToolComponent
)
from core.systems import CombatSystem, EconomySystem, FactionBehaviorSystem, GatheringSystem
from game_state import GameState
from init import initialize_game
from ui_style import (
    BUTTON_COLOR, BUTTON_FONT_SIZE, BUTTO<PERSON>_HOVER_COLOR, BUTTON_SPACING,
    GRID_START_X, GRID_START_Y, ITEM_PADDING, ITEM_SIZE
)
from ui_util import (
    draw_button, draw_input_box, draw_multiline_text, draw_scrollable_text_panel,
    draw_text, draw_tile_info, draw_titletext, draw_wrapped_text
)
from game_utils import safe_quit, seed_to_int

# Initialize logging and pygame
logging.basicConfig(level=logging.ERROR)
pygame.init()

# Suppress unnecessary Pygame warnings
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'

# Global variables
screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
pygame.display.set_caption("Living World RPG")
clock = pygame.time.Clock()
font = pygame.font.Font(None, TEXT_FONT_SIZE)

# Initialize the game using init.py
ecs_world, data, world_manager = initialize_game()

# Global configuration variables
window_config = data.get("window_config", {})
window_width = window_config.get("window_width", 1000)
window_height = window_config.get("window_height", 800)
tile_size = data.get("tile_size", 40)
player_speed = data.get("player_speed", 18.0)
companion_speed = data.get("companion_speed", 5.0)
font_size = data.get("font_size", 30)
color_palette = data.get("color_palette", {})
fallback_colors = color_palette.get("fallbacks", {})

# Global data for races and biomes
races = data.get("races", {})
biomes = data.get("biomes", {})

# Initialize display and font
try:
    screen = pygame.display.set_mode((window_width, window_height), pygame.RESIZABLE)
    pygame.display.set_caption(data.get("game_title", "Living World RPG"))
except pygame.error:
    screen = pygame.Surface((window_width, window_height))
font = pygame.font.SysFont(None, font_size)

# Global clock
clock = pygame.time.Clock()

# Helper Functions

def get_fallback_color(entity_type):
    hex_color = fallback_colors.get(entity_type, "#A9A9A9")
    r = int(hex_color[1:3], 16)
    g = int(hex_color[3:5], 16)
    b = int(hex_color[5:7], 16)
    return (r, g, b)

def update_camera_dimensions():
    camera_width = max(1, window_width // tile_size)
    camera_height = max(1, window_height // tile_size)
    return camera_width, camera_height

def update_camera_component():
    """Update the camera component dimensions based on current window size"""
    cam_w, cam_h = update_camera_dimensions()
    cam_comp = camera_entity.get_component(CameraComponent)
    if cam_comp:
        cam_comp.width = cam_w
        cam_comp.height = cam_h
        



def toggle_fullscreen(gs):
    global screen
    gs.is_fullscreen = not gs.is_fullscreen
    flags = pygame.FULLSCREEN if gs.is_fullscreen else pygame.RESIZABLE
    screen = pygame.display.set_mode((window_width, window_height), flags)

    # Update camera dimensions and simulation radius
    update_camera_component()

    # Update loading radius based on new camera size
    cam_w, cam_h = update_camera_dimensions()
    if world_manager:
        rs = world_manager.terrain_generator.region_size
        world_manager.simulation_radius = max(math.ceil(cam_w/rs), math.ceil(cam_h/rs))
        # Force update of active regions to ensure visibility
        world_manager.update_active_regions(gs.player_x, gs.player_y)

    

def handle_resize(evt):
    global screen, window_width, window_height
    window_width, window_height = evt.w, evt.h

    # Reset display surface
    screen = pygame.display.set_mode((window_width, window_height), pygame.RESIZABLE)

    # Update camera dimensions
    update_camera_component()

    # Update simulation radius
    cam_w, cam_h = update_camera_dimensions()
    if world_manager:
        rs = world_manager.terrain_generator.region_size
        world_manager.simulation_radius = max(math.ceil(cam_w/rs), math.ceil(cam_h/rs))
        # We don't have access to the game state here, so we can't update active regions
        # This will be handled by the screen that processes this event

    

def get_current_tile(gs):
    if not world_manager:
        logging.error("[Main] WorldManager not initialized.")
        return {}
    tilemap = world_manager.world_map.get("tiles", [])
    if not tilemap:
        logging.error("[Main] Tilemap empty.")
        return {}
    player_tile_x = int(gs.player_x)
    player_tile_y = int(gs.player_y)
    if (0 <= player_tile_x < len(tilemap[0])) and (0 <= player_tile_y < len(tilemap)):
        return tilemap[player_tile_y][player_tile_x]
    else:
        logging.error(f"[Main] Player position ({player_tile_x}, {player_tile_y}) out of bounds.")
        return {}

def generate_preview(gs):
    if world_manager and world_manager.terrain_generator:
        try:
            # Store original settings
            original_size = world_manager.terrain_generator.chunk_size
            original_biome = None
            if hasattr(world_manager.terrain_generator, 'starting_biome'):
                original_biome = world_manager.terrain_generator.starting_biome

            # Process seed value
            seed_val = gs.selected_seed
            if not seed_val:
                preview_seed = random.randint(0, 99999999)
                gs.selected_seed = str(preview_seed)
            elif not seed_val.isdigit():
                preview_seed = int(hashlib.sha256(seed_val.encode()).hexdigest(), 16) % (10**8)
            else:
                preview_seed = int(seed_val)

            # Set up terrain generator with the seed
            tgen = world_manager.terrain_generator
            tgen.seed = preview_seed

            # Temporarily disable the starting biome constraint to show variety
            tgen.starting_biome = None
            
            # Force regeneration of biome grid
            if hasattr(tgen, '_full_biome_cache'):
                delattr(tgen, '_full_biome_cache')

            # Use a smaller chunk size for the preview
            preview_size = 100  # Size of the preview map
            tgen.chunk_size = 20  # Smaller chunks for preview

            # Generate the environmental maps
            tgen.generate_heightmap()
            tgen.generate_temperature_map(rotation_deg=random.uniform(-45, 45))
            tgen.generate_rainfall_map(rotation_deg=random.uniform(-45, 45))

            # Generate a full biome grid for the preview
            biome_grid, heightmap, temp_map, rain_map = tgen.generate_full_biome_grid(
                preview_size, preview_size, preview_seed, tgen.biome_data
            )

            # Convert the biome grid to the format expected by the preview renderer
            preview_tiles = []
            for y in range(preview_size):
                row = []
                for x in range(preview_size):
                    tile = {
                        'x': x,
                        'y': y,
                        'biome': biome_grid[y, x],
                        'elevation': float(heightmap[y, x]),
                        'temperature': float(temp_map[y, x]),
                        'rainfall': float(rain_map[y, x])
                    }
                    row.append(tile)
                preview_tiles.append(row)

            # Find a settlement location in the player's chosen biome
            settlement_pos = None
            if hasattr(gs, 'player_biome_name') and gs.player_biome_name:
                suitable_locations = []
                for y in range(preview_size):
                    for x in range(preview_size):
                        if biome_grid[y, x] == gs.player_biome_name:
                            suitable_locations.append((x, y))

                if suitable_locations:
                    center_locations = []
                    edge_buffer = int(preview_size * 0.1)
                    for x, y in suitable_locations:
                        if (edge_buffer <= x < preview_size - edge_buffer and
                            edge_buffer <= y < preview_size - edge_buffer):
                            center_locations.append((x, y))

                    if center_locations:
                        settlement_pos = random.choice(center_locations)
                    else:
                        settlement_pos = random.choice(suitable_locations)

            # Create a preview map with all the generated tiles and additional data
            preview_chunk = {
                'tiles': preview_tiles,
                'settlement': settlement_pos,
                'heightmap': heightmap.tolist(),  # Convert numpy array to list for JSON serialization
                'temperature_map': temp_map.tolist(),
                'rainfall_map': rain_map.tolist(),
                'size': preview_size
            }

            # Store the preview map in game state
            gs.preview_map = preview_chunk

            # Restore original settings
            if original_biome is not None:
                tgen.starting_biome = original_biome
            tgen.chunk_size = original_size

        except Exception as e:
            logging.error(f"[Preview] Failed to generate preview: {e}")
            import traceback
            logging.error(traceback.format_exc())
            gs.preview_map = None
    else:
        logging.error("[Preview] World manager or terrain generator not initialized")

def create_npc_with_ai(name, x, y, behavior_type="basic", faction="neutral", creature_behavior="Neutral"):
    """
    Create an NPC entity with AI capabilities.

    Args:
        name: The name of the NPC
        x, y: Position coordinates
        behavior_type: AI behavior type (basic, behavior_tree, goap)
        faction: The faction this NPC belongs to
        creature_behavior: Movement behavior (Neutral, Aggressive, Passive, Friendly)

    Returns:
        The created entity
    """
    from core.components import AIComponent, PositionComponent, CreatureComponent, InventoryComponent, StatsComponent

    npc = ecs_world.create_entity()
    npc.add_component(PositionComponent(x, y))

    # Create creature with specified behavior for movement
    threat_level = "Medium"
    if creature_behavior == "Aggressive":
        threat_level = "High"
    elif creature_behavior == "Passive":
        threat_level = "Low"

    npc.add_component(CreatureComponent(name, threat_level, creature_behavior))
    npc.add_component(InventoryComponent())
    npc.add_component(StatsComponent())

    # Add AI component
    ai_comp = AIComponent(behavior_type)
    npc.add_component(ai_comp)

    # Set up personality traits based on random values
    for trait in ai_comp.personality:
        ai_comp.set_personality_trait(trait, random.random())

    # Set up initial schedule based on behavior type
    if behavior_type == "behavior_tree":
        from ai.behavior_tree import BehaviorTree, Selector, Sequence, Action, Condition, Blackboard, NodeStatus

        # Create a simple behavior tree for the NPC
        blackboard = Blackboard()
        blackboard.set("entity", npc)

        # Define conditions and actions
        is_hungry = Condition(lambda bb: bb.get("hunger", 0) > 70, "IsHungry")
        find_food = Action(lambda bb: NodeStatus.SUCCESS, "FindFood")
        eat_food = Action(lambda bb: NodeStatus.SUCCESS, "EatFood")

        is_tired = Condition(lambda bb: bb.get("energy", 0) < 30, "IsTired")
        find_bed = Action(lambda bb: NodeStatus.SUCCESS, "FindBed")
        sleep = Action(lambda bb: NodeStatus.SUCCESS, "Sleep")

        wander = Action(lambda bb: NodeStatus.SUCCESS, "Wander")

        # Build the tree
        hungry_sequence = Sequence("HungrySequence")
        hungry_sequence.add_child(is_hungry)
        hungry_sequence.add_child(find_food)
        hungry_sequence.add_child(eat_food)

        tired_sequence = Sequence("TiredSequence")
        tired_sequence.add_child(is_tired)
        tired_sequence.add_child(find_bed)
        tired_sequence.add_child(sleep)

        root = Selector("Root")
        root.add_child(hungry_sequence)
        root.add_child(tired_sequence)
        root.add_child(wander)

        # Create the behavior tree and store it in the AI component
        ai_comp.behavior_tree = BehaviorTree(root)
        ai_comp.blackboard = blackboard

    elif behavior_type == "goap":
        from ai.goap import GoapAgent, WorldState, Goal, Action

        # Create a GOAP agent for the NPC
        agent = GoapAgent(name)

        # Define actions
        gather_action = Action("GatherResource", cost=1)
        gather_action.add_precondition("has_resource", False)
        gather_action.add_effect("has_resource", True)

        store_action = Action("StoreResource", cost=1)
        store_action.add_precondition("has_resource", True)
        store_action.add_precondition("at_storage", True)
        store_action.add_effect("has_resource", False)
        store_action.add_effect("resources_stored", True)

        move_to_storage = Action("MoveToStorage", cost=2)
        move_to_storage.add_precondition("at_storage", False)
        move_to_storage.add_effect("at_storage", True)

        # Add actions to the agent
        agent.add_action(gather_action)
        agent.add_action(store_action)
        agent.add_action(move_to_storage)

        # Define a goal
        goal = Goal("StoreResources")
        goal.add_condition("resources_stored", True)
        agent.add_goal(goal)

        # Set current goal
        agent.set_current_goal("StoreResources")

        # Set initial world state
        agent.world_state.set("has_resource", False)
        agent.world_state.set("at_storage", False)
        agent.world_state.set("resources_stored", False)

        # Store the agent in the AI component
        ai_comp.goap_agent = agent

    
    return npc

def initialize_world(gs):
    if not world_manager:
        logging.error("[Main] World manager not initialized!")
        return False

    try:
        if gs.player_biome_name:
            world_manager.terrain_generator.set_starting_biome(gs.player_biome_name)

        world_manager.init_world()
        player_entity = ecs_world.get_player_entity()
        if player_entity:
            pass
        else:
            player_entity = ecs_world.create_entity()
            player_entity.add_component(PlayerComponent("Hero"))
            player_entity.add_component(PositionComponent(gs.player_x, gs.player_y))
            player_entity.add_component(StatsComponent())
            player_entity.add_component(CombatComponent(attack=12, defense=5))
            player_entity.add_component(InventoryComponent())
            player_entity.add_component(ToolComponent())
            player_entity.add_component(GatheringSkillComponent())
            # Add ReputationComponent to track player reputation with factions
            initial_rep = {fname: 0 for fname in data.get("factions", {}).keys()}
            player_entity.add_component(ReputationComponent(initial_rep))

            # Give player some starting tools
            tool_comp = player_entity.get_component(ToolComponent)
            tool_comp.equip_tool("axe", {"durability": 100, "efficiency": 1.0})
            tool_comp.equip_tool("pickaxe", {"durability": 100, "efficiency": 1.0})

            # Add GatheringSystem if not already added
            gathering_system = None
            for system in ecs_world.systems:
                if isinstance(system, GatheringSystem):
                    gathering_system = system
                    break
            if not gathering_system:
                gathering_system = GatheringSystem()
                ecs_world.add_system(gathering_system)
            

            # Add FactionBehaviorSystem if missing
            if not any(isinstance(s, FactionBehaviorSystem) for s in ecs_world.systems):
                ecs_world.add_system(FactionBehaviorSystem())
                

            # Add EconomySystem if missing
            if not any(isinstance(s, EconomySystem) for s in ecs_world.systems):
                # Pass the world's EconomyManager for dynamic pricing
                ecs_world.add_system(EconomySystem(data.get("items", {}), world_manager.economy_manager))
                

            # Add AISystem if missing
            from core.systems import AISystem
            if not any(isinstance(s, AISystem) for s in ecs_world.systems):
                ecs_world.add_system(AISystem())

            # Add MovementSystem if missing
            from core.systems import MovementSystem
            if not any(isinstance(s, MovementSystem) for s in ecs_world.systems):
                ecs_world.add_system(MovementSystem())

            # Create some NPCs with different AI behaviors and movement patterns
            create_npc_with_ai("Villager Bob", gs.player_x + 3, gs.player_y + 2, "behavior_tree", "neutral", "Friendly")
            create_npc_with_ai("Merchant Alice", gs.player_x - 2, gs.player_y + 4, "goap", "neutral", "Neutral")
            create_npc_with_ai("Guard Tom", gs.player_x + 5, gs.player_y - 1, "basic", "neutral", "Neutral")

            # Add some creatures with different movement behaviors
            create_npc_with_ai("Wolf", gs.player_x + 8, gs.player_y + 8, "basic", "wild", "Aggressive")
            create_npc_with_ai("Rabbit", gs.player_x - 6, gs.player_y - 3, "basic", "wild", "Passive")
            create_npc_with_ai("Deer", gs.player_x - 10, gs.player_y + 5, "basic", "wild", "Passive")

        # Log player components for debug
        comp_names = [type(c).__name__ for c in player_entity.components.values()]
        
        gs.started_game = True
        return True
    except Exception as e:
        logging.error(f"[Main] Error initializing world: {e}")
        return False

# Create a global camera entity (only once)
camera_width, camera_height = update_camera_dimensions()
# We create the camera entity only once at startup.
camera_entity = ecs_world.create_entity()
camera_entity.add_component(CameraComponent(x=0, y=0, width=camera_width, height=camera_height))
camera_entity.add_component(PositionComponent(0, 0))


# --- DEBUG: Export Biome Grid ---
def export_biome_grid_debug():
    if hasattr(world_manager, 'terrain_generator') and hasattr(world_manager.terrain_generator, 'debug_output_biome_grid'):
        ascii_path = 'biome_grid_debug.txt'
        json_path = 'biome_grid_debug.json'
        world_manager.terrain_generator.debug_output_biome_grid(filename_ascii=ascii_path, filename_json=json_path)
        print(f"[DEBUG] Exported biome grid to {ascii_path} and {json_path}")
    else:
        print("[DEBUG] Terrain generator or debug output not available.")

# --- DEBUG: Test AI System ---
def test_ai_system():
    """Test the AI system by creating NPCs and running their AI."""
    from core.components import AIComponent, PositionComponent
    from ai.behavior_tree import BehaviorTree, Selector, Sequence, Action, Condition, Blackboard, NodeStatus

    # Create test NPCs
    npc1 = create_npc_with_ai("Test Villager", 10, 10, "behavior_tree", "neutral", "Friendly")
    npc2 = create_npc_with_ai("Test Merchant", 15, 15, "goap", "neutral", "Neutral")
    npc3 = create_npc_with_ai("Test Wolf", 20, 20, "basic", "wild", "Aggressive")

    # Get AI components
    ai1 = npc1.get_component(AIComponent)
    ai2 = npc2.get_component(AIComponent)
    ai3 = npc3.get_component(AIComponent)

    # Test behavior tree AI
    if ai1 and ai1.behavior_type == "behavior_tree" and hasattr(ai1, "behavior_tree"):
        # Set up test conditions
        ai1.blackboard["hunger"] = 80  # Make the NPC hungry

        # Run the behavior tree
        status = ai1.behavior_tree.tick()
        print(f"[DEBUG] Behavior tree status: {status}")

    # Test GOAP AI
    if ai2 and ai2.behavior_type == "goap" and hasattr(ai2, "goap_agent"):
        # Set up test conditions
        ai2.goap_agent.world_state.set("has_resource", True)

        # Run the GOAP planner
        action = ai2.goap_agent.think()
        if action:
            print(f"[DEBUG] GOAP selected action: {action.name}")
        else:
            print(f"[DEBUG] GOAP found no valid action")

    # Test basic AI with aggressive behavior
    if ai3 and ai3.behavior_type == "basic":
        creature_comp = npc3.get_component(CreatureComponent)
        print(f"[DEBUG] Testing basic AI for {creature_comp.creature_name} with {creature_comp.behavior} behavior")

        # Test the movement system with this creature
        from core.systems import MovementSystem
        movement_sys = MovementSystem()
        movement_sys.world = ecs_world
        movement_sys.entities = [npc3]
        movement_sys.update(0.1, current_time=0)  # Small time step for testing

    print("[DEBUG] AI system test complete")

# --- DEBUG: Generate Multi-Chunk Preview ---
def generate_debug_preview(seed_value=None):
    """Generate a multi-chunk preview for debugging purposes."""
    if not world_manager or not world_manager.terrain_generator:
        print("[DEBUG] World manager or terrain generator not available.")
        return

    tgen = world_manager.terrain_generator

    # Save original settings
    original_seed = tgen.seed
    original_biome = None
    if hasattr(tgen, 'starting_biome'):
        original_biome = tgen.starting_biome
    original_chunk_size = tgen.chunk_size

    # Set up for preview
    if seed_value:
        tgen.seed = seed_value
    tgen.starting_biome = None  # Disable starting biome constraint
    tgen.chunk_size = 32  # Smaller chunks for preview

    # Generate maps
    tgen.generate_heightmap()
    tgen.generate_temperature_map(rotation_deg=random.uniform(-45, 45))
    tgen.generate_rainfall_map(rotation_deg=random.uniform(-45, 45))

    # Clear any cached biome grid
    if hasattr(tgen, '_full_biome_cache'):
        delattr(tgen, '_full_biome_cache')

    # Generate chunks
    preview_grid_size = 5
    chunks = {}
    biomes_found = set()

    print(f"[DEBUG] Generating {preview_grid_size}x{preview_grid_size} grid of chunks")
    for cy in range(preview_grid_size):
        for cx in range(preview_grid_size):
            chunk_x = cx - preview_grid_size // 2
            chunk_y = cy - preview_grid_size // 2
            print(f"[DEBUG] Generating chunk at ({chunk_x}, {chunk_y})")

            chunk = tgen.generate_world_chunk(chunk_x, chunk_y)
            chunks[(chunk_x, chunk_y)] = chunk

            if chunk and 'tiles' in chunk:
                for row in chunk['tiles']:
                    for tile in row:
                        biomes_found.add(tile.get('biome', 'Unknown'))

    print(f"[DEBUG] Found biomes: {biomes_found}")

    # Restore original settings
    tgen.seed = original_seed
    if original_biome is not None:
        tgen.starting_biome = original_biome
    tgen.chunk_size = original_chunk_size

    return chunks

# -------------------------
# Object-Oriented Screen Classes
# -------------------------

from constants import (
    COLOR_WHITE, COLOR_BLACK, COLOR_GREEN, COLOR_RED,
    WINDOW_WIDTH, WINDOW_HEIGHT,
    BUTTON_WIDTH, BUTTON_HEIGHT, BUTTON_SPACING,
    BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR,
    BIOME_COLORS
)

from screens.abstract_screen import AbstractScreen
from ui_util import draw_text, draw_button, draw_wrapped_text

class Screen(AbstractScreen):
    """Base class for game screens.

    This class provides common functionality for all game screens.
    """

    def __init__(self, gs: GameState) -> None:
        """Initialize the screen with the game state.

        Args:
            gs (GameState): The game state object
        """
        super().__init__(gs)

    def handle_events(self) -> None:
        """Handle events for this screen."""
        try:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.gs.current_state = "quit"
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.gs.current_state = "quit"
                elif event.type == pygame.VIDEORESIZE:
                    self.handle_resize(event)
        except Exception as e:
            logging.error(f"Error handling events in {self.__class__.__name__}: {str(e)}")
            raise

    def update(self, dt: float) -> None:
        """Update the screen state.

        Args:
            dt (float): Time delta since last update
        """
        pass

    def render(self, screen: pygame.Surface) -> None:
        """Render the screen content.

        Args:
            screen (pygame.Surface): The surface to render to
        """
        try:
            screen.fill(COLOR_BLACK)
            
            # Draw title
            draw_text(screen, self.font, self.get_screen_title(), 
                     WINDOW_WIDTH // 2, 30, COLOR_WHITE, "center")
            
            # Draw close button
            close_rect = draw_button(screen, self.font, "X", 
                                   WINDOW_WIDTH - BUTTON_WIDTH - 10, 10,
                                   BUTTON_WIDTH, BUTTON_HEIGHT,
                                   BUTTON_FONT_SIZE, COLOR_RED, COLOR_RED)
            
            # Store close button rect for event handling
            self.close_button_rect = close_rect
            
        except Exception as e:
            logging.error(f"Error rendering {self.__class__.__name__}: {str(e)}")
            raise

    def handle_resize(self, event) -> None:
        """Handle window resize events."""
        global WINDOW_WIDTH, WINDOW_HEIGHT
        WINDOW_WIDTH = event.w
        WINDOW_HEIGHT = event.h
        
        # Update any screen-specific dimensions
        self.update_dimensions()

    def update_dimensions(self) -> None:
        """Update screen dimensions after resize."""
        pass

    def get_screen_title(self) -> str:
        """Get the screen title."""
        return self.__class__.__name__.replace("Screen", "")

    def show_error(self, message: str) -> None:
        """Show an error message."""
        self.gs.show_error_message(message)

    def show_success(self, message: str) -> None:
        """Show a success message."""
        self.gs.show_success_message(message)

# --- Seed utility function ---


# -------------------------
# Screen Implementations
# -------------------------

class StartMenuScreen(Screen):
    def __init__(self, gs: GameState) -> None:
        super().__init__(gs)
        try:
            self.start_button_rect: Optional[pygame.Rect] = None
            self.quit_button_rect: Optional[pygame.Rect] = None
            self.resume_button_rect: Optional[pygame.Rect] = None
            self.close_button_rect: pygame.Rect = pygame.Rect(0, 0, 0, 0)
        except Exception as e:
            logging.error(f"Error initializing StartMenuScreen: {str(e)}")
            raise

    def handle_events(self) -> None:
        try:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    safe_quit()
                elif event.type == pygame.VIDEORESIZE:
                    handle_resize(event)
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_f:
                        toggle_fullscreen(self.gs)
                elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    mouse_pos = pygame.mouse.get_pos()
                    if self.resume_button_rect and self.resume_button_rect.collidepoint(mouse_pos):
                        self.gs.current_state = "gameplay"
                    elif self.quit_button_rect and self.quit_button_rect.collidepoint(mouse_pos):
                        self.gs.current_state = "character_selection"
                        logging.info("[StartMenu] Transitioning to Character Selection.")
                    elif self.start_button_rect and self.start_button_rect.collidepoint(mouse_pos):
                        self.gs.current_state = "character_selection"
                        logging.info("[StartMenu] Transitioning to Character Selection.")
                    elif self.quit_button_rect and self.quit_button_rect.collidepoint(mouse_pos):
                        safe_quit()
        except Exception as e:
            logging.error(f"Error handling events in StartMenuScreen: {str(e)}")
            raise

    def update(self, dt: float) -> None:
        """Update the screen state. No updates needed for start menu."""
        pass

    def render(self, screen: pygame.Surface) -> None:
        """Render the start menu screen."""
        try:
            screen.fill((30, 30, 30))
            draw_titletext(screen, font, "Living World RPG", window_width // 2, 30)
            
            # Draw buttons
            button_width = 200
            button_height = 50
            button_spacing = 20
            
            # Start button
            start_y = 150
            self.start_button_rect = draw_button(
                screen, "Start New Game", 
                (window_width // 2 - button_width // 2, start_y), 
                button_width, button_height
            )
            
            # Resume button
            resume_y = start_y + button_height + button_spacing
            self.resume_button_rect = draw_button(
                screen, "Resume Game", 
                (window_width // 2 - button_width // 2, resume_y), 
                button_width, button_height
            )
            
            # Quit button
            quit_y = resume_y + button_height + button_spacing
            self.quit_button_rect = draw_button(
                screen, "Quit Game", 
                (window_width // 2 - button_width // 2, quit_y), 
                button_width, button_height
            )
            
            # Close button
            self.close_button_rect = draw_button(
                screen, "X", 
                (window_width - 50, 10), 
                40, 40
            )
        except Exception as e:
            logging.error(f"Error rendering StartMenuScreen: {str(e)}")
            raise

    def update(self, dt: float):
        pass

    def render(self, screen: pygame.Surface):
        screen.fill((30,30,30))
        draw_titletext(screen, font, "Living World RPG", window_width//2, 70)

        if self.gs.started_game:
            is_hovered, self.resume_button_rect = draw_button(screen, font, "Resume Game", window_width//2, 300, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        is_hovered, self.start_button_rect = draw_button(screen, font, "Start Game", window_width//2, 400, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        is_hovered, self.quit_button_rect = draw_button(screen, font, "Quit Game", window_width//2, 500, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)

        pygame.display.flip()

class SeedSelectionScreen(Screen):
    def __init__(self, gs):
        super().__init__(gs)
        self.active_input = False
        self.input_box_rect = None
        self.random_button_rect = None
        self.preview_button_rect = None
        self.confirm_button_rect = None
        self.back_button_rect = None
        self.last_click_time = 0
        self.double_click_interval = 400  # ms
        self.selected_all = False
        self.last_seed = None  # Track last seed for preview update
        if not self.gs.selected_seed:
            self.gs.selected_seed = str(random.randint(0, 99999999))
        self.preview_needs_update = True

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                safe_quit()
            elif event.type == pygame.VIDEORESIZE:
                handle_resize(event)
            elif event.type == pygame.KEYDOWN:
                if self.active_input:
                    if self.selected_all:
                        self.gs.selected_seed = ""
                        self.selected_all = False
                    if event.key == pygame.K_BACKSPACE:
                        self.gs.selected_seed = self.gs.selected_seed[:-1]
                        self.preview_needs_update = True
                    elif event.key == pygame.K_RETURN:
                        self.active_input = False
                        self.generate_and_update_preview()
                    elif len(self.gs.selected_seed) < 16 and (event.unicode.isprintable()):
                        self.gs.selected_seed += event.unicode
                        self.preview_needs_update = True
                elif event.key == pygame.K_f:
                    toggle_fullscreen(self.gs)
            elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                mouse_pos = pygame.mouse.get_pos()
                if self.input_box_rect and self.input_box_rect.collidepoint(mouse_pos):
                    self.active_input = True
                    self.selected_all = True  # Select all on single click
                    self.last_click_time = pygame.time.get_ticks()
                else:
                    self.active_input = False
                    self.selected_all = False
                if self.random_button_rect and self.random_button_rect.collidepoint(mouse_pos):
                    self.gs.selected_seed = str(random.randint(0, 99999999))
                    self.preview_needs_update = True
                elif self.preview_button_rect and self.preview_button_rect.collidepoint(mouse_pos):
                    self.generate_and_update_preview()
                elif self.confirm_button_rect and self.confirm_button_rect.collidepoint(mouse_pos):
                    self.gs.selected_seed = self.gs.selected_seed or str(random.randint(0, 99999999))
                    final_seed = seed_to_int(self.gs.selected_seed)
                    if world_manager and world_manager.terrain_generator:
                        world_manager.terrain_generator.seed = final_seed
                        world_manager.starting_biome = self.gs.player_biome_name
                        initialize_world(self.gs)
                    self.gs.current_state = "loading"
                elif self.back_button_rect and self.back_button_rect.collidepoint(mouse_pos):
                    self.gs.current_state = "biome_selection"

    def generate_and_update_preview(self):
        seed_int = seed_to_int(self.gs.selected_seed)
        if world_manager and world_manager.terrain_generator:
            world_manager.terrain_generator.seed = seed_int
        generate_preview(self.gs)
        self.last_seed = self.gs.selected_seed
        self.preview_needs_update = False

    def update(self, dt: float):
        # Auto-update preview if seed changed
        if self.gs.selected_seed != self.last_seed:
            self.generate_and_update_preview()

    def render(self, screen: pygame.Surface):
        screen.fill((30, 30, 30))
        
        # Title
        title_text = "Seed Selection"
        draw_titletext(screen, font, title_text, window_width // 2, 30)
        
        # Description
        desc_text = "Enter a numeric or text seed (leave blank for random):"
        draw_text(screen, font, desc_text, 50, 70)
        
        # Input box
        seed_box_width = min(440, window_width - 120)
        seed_box_x = (window_width // 2) - (seed_box_width // 2)
        seed_box_y = 110  # Increased Y position for input box
        seed_box_rect = draw_input_box(screen, font, self.gs.selected_seed, seed_box_x, seed_box_y, seed_box_width, 40, self.active_input)
        
        # Button layout
        button_height = font.get_height() + 20
        button_width = max([font.size(btn)[0] for btn in ["Random Seed", "Preview", "Confirm", "Back"]]) + 40
        
        # Position buttons relative to screen
        button_x = window_width // 2 - button_width * 2
        button_y = seed_box_y + seed_box_rect.height + 30  # Position buttons below input box with spacing
        
        btns = ["Random Seed", "Preview", "Confirm", "Back"]
        btn_rects = []
        for i, btn in enumerate(btns):
            btn_x = button_x + i * (button_width + 10)
            btn_rect = draw_button(screen, font, btn, btn_x, button_y, button_width, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
            btn_rects.append(btn_rect)
            
        # Store button rectangles
        self.random_button_rect = pygame.Rect(btn_rects[0][1])
        self.preview_button_rect = pygame.Rect(btn_rects[1][1])
        self.confirm_button_rect = pygame.Rect(btn_rects[2][1])
        self.back_button_rect = pygame.Rect(btn_rects[3][1])

        # Draw the preview map
        if self.gs.preview_map:
            tmap = self.gs.preview_map.get("tiles", [])
            if tmap:
                # Calculate safe position for preview map
                preview_width = min(500, window_width - 120)
                preview_height = min(500, window_height - 120)
                preview_x = window_width - preview_width - 20
                preview_y = 250  # Increased Y position to avoid overlap
                
                # Draw preview map title
                draw_text(screen, font, "World Preview", preview_x, preview_y - 30)
                
                # Draw preview map
                preview_surface = pygame.Surface((preview_width, preview_height))
                preview_surface.fill((30, 30, 30))
                
                # Scale preview map to fit
                tile_size = min(preview_width // len(tmap[0]), preview_height // len(tmap))
                
                for y, row in enumerate(tmap):
                    for x, tile in enumerate(row):
                        biome = tile.get("biome")
                        color = BIOME_COLORS.get(biome, (0, 0, 0))
                        pygame.draw.rect(preview_surface, color, 
                                      (x * tile_size, y * tile_size, tile_size, tile_size))
                
                screen.blit(preview_surface, (preview_x, preview_y))
                
                # Draw settlement marker if available
                settlement_pos = self.gs.preview_map.get("settlement")
                if settlement_pos:
                    sx, sy = settlement_pos
                    pygame.draw.circle(screen, (255, 0, 0), 
                                    (preview_x + int(sx * tile_size), 
                                     preview_y + int(sy * tile_size)), 5)
                
                # Draw the map tiles
                for y, row in enumerate(tmap):
                    for x, tile in enumerate(row):
                        biome = tile.get('biome')
                        base_color = BIOME_COLORS.get(biome, (0, 0, 0))
                        
                        # Adjust color based on elevation and other factors
                        # Higher elevation = lighter color (mountains, hills)
                        # Lower elevation = darker color (valleys, lowlands)
                        elevation = tile.get('elevation', 0.5)  # Default to mid-range elevation if not present
                        brightness_factor = 0.7 + (elevation * 0.6)  # 0.7 to 1.3 range

                        # Apply brightness adjustment
                        adjusted_color = (
                            min(255, int(base_color[0] * brightness_factor)),
                            min(255, int(base_color[1] * brightness_factor)),
                            min(255, int(base_color[2] * brightness_factor))
                        )
                        
                        # Draw the tile
                        pygame.draw.rect(screen, adjusted_color,
                                      (preview_x + x * tile_size,
                                       preview_y + y * tile_size,
                                       tile_size, tile_size))

                # Add terrain features based on biome and environmental factors
                if biome == "Ocean" and tile_size >= 6:
                    # Draw wave patterns for ocean
                    wave_color = (max(0, adjusted_color[0] - 30),
                                 max(0, adjusted_color[1] - 30),
                                 max(0, adjusted_color[2] - 30))
                    pygame.draw.line(screen, wave_color,
                                   (preview_x + x * tile_size + tile_size//4,
                                    preview_y + y * tile_size + tile_size//4),
                                   (preview_x + x * tile_size + tile_size//4,
                                    preview_y + y * tile_size + tile_size//4 + tile_size//2),
                                   2)

                # Draw legend entries for each biome
                for i, biome in enumerate(sorted(self.gs.preview_map.get("biomes", []))):
                    # Calculate position (2 columns if more than 4 biomes)
                    if len(self.gs.preview_map.get("biomes", [])) > 4 and i >= len(self.gs.preview_map.get("biomes", [])) // 2:
                        # Second column
                        x = preview_x + preview_width // 2
                        y = preview_y + (i - len(self.gs.preview_map.get("biomes", [])) // 2) * 25
                    else:
                        # First column
                        x = preview_x
                        y = preview_y + i * 25

                    # Get biome color
                    bhex = data.get("color_palette", {}).get("biomes", {}).get(biome, "#808080")
                    try:
                        color = tuple(int(bhex.strip('#')[i:i+2], 16) for i in (0, 2, 4))
                    except (ValueError, IndexError):
                        color = (128, 128, 128)

                    # Draw color square and biome name
                    pygame.draw.rect(screen, color, (x, y, 15, 15))
                    pygame.draw.rect(screen, (0, 0, 0), (x, y, 15, 15), 1)  # Black outline
                    draw_text(screen, font, biome, x + 25, y + 7, color=(255, 255, 255))

                    # Mark player's chosen biome with an asterisk and highlight
                    if biome == self.gs.player_biome_name:
                        # Draw highlight behind the text
                        text_width = len(biome) * 10  # Approximate width
                        pygame.draw.rect(screen, (60, 60, 100),
                                       (x + 20, y, text_width + 15, 20))
                        # Redraw text on top of highlight
                        draw_text(screen, font, biome, x + 25, y + 7, color=(255, 255, 100))
                        draw_text(screen, font, "★", x + 20, y + 7, color=(255, 255, 0))
                for tile in row:
                    unique_biomes.add(tile.get("biome", "Unknown"))

            # Sort biomes alphabetically
            sorted_biomes = sorted(list(unique_biomes))

            # Calculate legend height based on content
            biome_rows = math.ceil(len(sorted_biomes) / 2)  # 2 columns
            terrain_features_rows = 4  # Mountains, Forests, Water, Settlement
            legend_height = (biome_rows + terrain_features_rows + 2) * legend_spacing + 20

            # Draw legend background
            pygame.draw.rect(screen, (40, 40, 40),
                           (legend_x - 10, legend_y - 10,
                            legend_width + 20, legend_height))
            pygame.draw.rect(screen, (100, 100, 100),
                           (legend_x - 10, legend_y - 10,
                            legend_width + 20, legend_height), 2)

            # Title for the biomes section
            draw_text(screen, font, "Biomes:", legend_x, legend_y, color=(255, 255, 0))
            legend_y += 25

            # Draw legend entries for each biome
            for i, biome in enumerate(sorted_biomes):
                # Calculate position (2 columns if more than 4 biomes)
                if len(sorted_biomes) > 4 and i >= len(sorted_biomes) // 2:
                    # Second column
                    x = legend_x + legend_width // 2
                    y = legend_y + (i - len(sorted_biomes) // 2) * legend_spacing
                else:
                    # First column
                    x = legend_x
                    y = legend_y + i * legend_spacing

                # Get biome color
                bhex = data.get("color_palette", {}).get("biomes", {}).get(biome, "#808080")
                try:
                    color = tuple(int(bhex.strip('#')[i:i+2], 16) for i in (0, 2, 4))
                except (ValueError, IndexError):
                    color = (128, 128, 128)

                # Draw color square and biome name
                pygame.draw.rect(screen, color, (x, y, 15, 15))
                pygame.draw.rect(screen, (0, 0, 0), (x, y, 15, 15), 1)  # Black outline
                draw_text(screen, font, biome, x + 25, y + 7, color=(255, 255, 255))

                # Mark player's chosen biome with an asterisk and highlight
                if biome == self.gs.player_biome_name:
                    # Draw highlight behind the text
                    text_width = len(biome) * 10  # Approximate width
                    pygame.draw.rect(screen, (60, 60, 100),
                                   (x + 20, y, text_width + 15, 20))
                    # Redraw text on top of highlight
                    draw_text(screen, font, biome, x + 25, y + 7, color=(255, 255, 100))
                    draw_text(screen, font, "★", x + 20, y + 7, color=(255, 255, 0))

            # Move to the terrain features section
            terrain_y = legend_y + (biome_rows + 1) * legend_spacing
            draw_text(screen, font, "Terrain Features:", legend_x, terrain_y, color=(255, 255, 0))
            terrain_y += 25

            # Draw mountain feature
            mountain_x = legend_x
            mountain_y = terrain_y
            mountain_color = (200, 200, 200)
            # Draw a triangle for mountains
            peak_height = 12
            points = [
                (mountain_x + 7, mountain_y),
                (mountain_x, mountain_y + peak_height),
                (mountain_x + 15, mountain_y + peak_height)
            ]
            pygame.draw.polygon(screen, mountain_color, points)
            draw_text(screen, font, "Mountains (High Elevation)", mountain_x + 25, mountain_y + 7, color=(255, 255, 255))

            # Draw forest feature
            forest_x = legend_x + legend_width // 2
            forest_y = terrain_y
            forest_color = (0, 150, 0)
            pygame.draw.circle(screen, forest_color, (forest_x + 7, forest_y + 7), 7)
            draw_text(screen, font, "Forests & Vegetation", forest_x + 25, forest_y + 7, color=(255, 255, 255))

            # Draw water feature
            water_x = legend_x
            water_y = terrain_y + legend_spacing
            water_color = (0, 100, 200)
            pygame.draw.rect(screen, water_color, (water_x, water_y, 15, 15))
            pygame.draw.line(screen, (0, 70, 150),
                           (water_x + 3, water_y + 7),
                           (water_x + 12, water_y + 3), 2)
            draw_text(screen, font, "Ocean & Water", water_x + 25, water_y + 7, color=(255, 255, 255))

            # Draw settlement feature
            settlement_x = legend_x + legend_width // 2
            settlement_y = terrain_y + legend_spacing
            settlement_color = data.get("color_palette", {}).get("settlements", {}).get("Medium Settlement", "#FFD700")
            try:
                color = tuple(int(settlement_color.strip('#')[i:i+2], 16) for i in (0, 2, 4))
            except (ValueError, IndexError):
                color = (255, 215, 0)  # Gold fallback

            # Draw settlement circle
            pygame.draw.circle(screen, color, (settlement_x + 7, settlement_y + 7), 7)
            pygame.draw.circle(screen, (0, 0, 0), (settlement_x + 7, settlement_y + 7), 7, 1)  # Black outline

            # Draw small building
            pygame.draw.rect(screen, (100, 50, 0), (settlement_x + 4, settlement_y + 4, 6, 6))

            # Draw settlement text
            draw_text(screen, font, "Settlement (Your Starting Location)", settlement_x + 25, settlement_y + 7, color=(255, 255, 255))

            # Add note about the preview
            note_y = terrain_y + 2 * legend_spacing
            draw_text(screen, font, "Note: This preview shows the world generated with your chosen seed.",
                     legend_x, note_y + 15, color=(200, 200, 200))
            draw_text(screen, font, "Your settlement will be placed in your chosen biome.",
                     legend_x, note_y + 35, color=(200, 200, 200))
        elif self.preview_needs_update:
            # Show loading/placeholder if preview is being generated
            draw_text(screen, font, "Generating preview...", window_width//2, window_height//2)
        pygame.display.flip()

class CharacterSelectionScreen(Screen):
    def __init__(self, gs):
        super().__init__(gs)
        self.race_buttons = []
        self.confirm_button_rect = None
        self.back_button_rect = None

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                safe_quit()
            elif event.type == pygame.VIDEORESIZE:
                handle_resize(event)
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_f:
                    toggle_fullscreen(self.gs)
            elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                mouse_pos = pygame.mouse.get_pos()

                # Check race selection buttons
                for i, button_rect in enumerate(self.race_buttons):
                    if button_rect.collidepoint(mouse_pos):
                        self.gs.selected_race_index = i
                        break

                # Check confirm and back buttons
                if self.confirm_button_rect and self.confirm_button_rect.collidepoint(mouse_pos):
                    race_list = list(races.keys())
                    if race_list:
                        self.gs.player_race_name = race_list[self.gs.selected_race_index]
                        self.gs.current_state = "biome_selection"
                        logging.info(f"[CharacterSelection] Race confirmed: {self.gs.player_race_name}")
                elif self.back_button_rect and self.back_button_rect.collidepoint(mouse_pos):
                    self.gs.current_state = "start_menu"
                    

    def update(self, dt: float):
        pass

    def render(self, screen: pygame.Surface):
        screen.fill((30,30,30))
        draw_text(screen, font, "Select Your Race", 76, 30)

        # Clear previous button rectangles
        self.race_buttons = []

        # Draw race selection buttons
        race_list = list(races.keys())
        y = 60
        for i, r_name in enumerate(race_list):
            is_hovered, button_rect = draw_button(screen, font, r_name, 160, y, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
            self.race_buttons.append(button_rect)
            y += 50

        # Draw race details if a race is selected
        if race_list:
            curr_race = race_list[self.gs.selected_race_index]
            details = races.get(curr_race, {})
            dx, dy = 300, 60
            draw_text(screen, font, "Race Details:", dx, dy)
            dy += 40  # Increased spacing

            # Draw placeholder image
            pygame.draw.rect(screen, (50,50,50), (dx, dy, 150, 150))
            draw_wrapped_text(screen, font, "Image Coming Soon", dx + 5, dy+50, 150)

            # Get descriptions and stats
            desc = details.get("description", "No description available.")
            base_stats = details.get("base_stats", {})
            stats_str = f"HP: {base_stats.get('hp', 'N/A')}  MP: {base_stats.get('mp', 'N/A')}"

            # Draw stats with proper spacing
            draw_wrapped_text(screen, font, f"{stats_str}", dx + 170, dy, 600)
            dy += 40  # Increased spacing

            # Process starting skills with adjusted spacing
            starting_skills = details.get("starting_skills", {})
            skills_lines = []
            skills_title = ["Starting Skills:"]
            draw_text(screen, font, skills_title[0], dx + 170, dy)


            for skill, info in starting_skills.items():
                level = info.get("level", 1)
                description = info.get("description", "")
                skills_lines.append(f"{skill} (Lvl {level}): {description}")
            skills_str = "\n".join(skills_lines)
            draw_multiline_text(screen, font, skills_str, dx + 170, dy+40, 510)
            dy += 120  # Increased spacing for skills section

            # Process unique traits with adjusted spacing
            unique_traits = details.get("unique_traits", [])
            traits_lines = []
            traits_title = ["Unique Traits:"]
            for traits in unique_traits:
                name = traits.get("name", "Unnamed")
                effect = traits.get("effect", "")
                traits_lines.append(f"{name}: {effect}")
            traits_str = "\n".join(traits_lines)

            # Draw description and traits with proper spacing
            dy += 50  # Additional spacing before description
            draw_wrapped_text(screen, font, f"Description: {desc}", dx, dy, 600)
            dy += 80  # Increased spacing before traits
            draw_text(screen, font, traits_title[0], dx, dy)
            draw_multiline_text(screen, font, traits_str, dx, dy+40, 600)

        # Draw and store confirm/back button rectangles
        is_hovered, self.confirm_button_rect = draw_button(screen, font, "Confirm", window_width//2 - 100, window_height - 60, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        is_hovered, self.back_button_rect = draw_button(screen, font, "Back", window_width//2 + 120, window_height - 60, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)

        pygame.display.flip()

class BiomeSelectionScreen(Screen):
    def __init__(self, gs):
        super().__init__(gs)
        self.biome_buttons = []
        self.confirm_button_rect = None
        self.back_button_rect = None

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                safe_quit()
            elif event.type == pygame.VIDEORESIZE:
                handle_resize(event)
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    self.gs.selected_biome_index = max(0, self.gs.selected_biome_index - 1)
                elif event.key == pygame.K_DOWN:
                    biome_list = list(biomes.keys())
                    self.gs.selected_biome_index = min(len(biomes)-1, self.gs.selected_biome_index + 1)
                elif event.key == pygame.K_f:
                    toggle_fullscreen(self.gs)
            elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                mouse_pos = pygame.mouse.get_pos()

                # Check biome selection buttons
                for i, button_rect in enumerate(self.biome_buttons):
                    if button_rect.collidepoint(mouse_pos):
                        self.gs.selected_biome_index = i
                        break

                # Check confirm and back buttons
                if self.confirm_button_rect and self.confirm_button_rect.collidepoint(mouse_pos):
                    biome_list = list(biomes.keys())
                    if biome_list:
                        self.gs.player_biome_name = biome_list[self.gs.selected_biome_index]
                        if world_manager:
                            world_manager.starting_biome = self.gs.player_biome_name
                            world_manager.terrain_generator.set_starting_biome(self.gs.player_biome_name)
                        self.gs.current_state = "seed_selection"
                        
                elif self.back_button_rect and self.back_button_rect.collidepoint(mouse_pos):
                    self.gs.current_state = "character_selection"
                    

    def update(self, dt: float):
        pass

    def render(self, screen: pygame.Surface):
        screen.fill((30,30,30))
        draw_titletext(screen, font, "Select Your Biome", window_width//4, 50)

        # Clear previous button rectangles
        self.biome_buttons = []

        # Draw biome selection buttons
        biome_list = list(biomes.keys())
        y = 100
        for i, b_name in enumerate(biome_list):
            is_hovered, button_rect = draw_button(screen, font, b_name, window_width//4, y, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
            self.biome_buttons.append(button_rect)
            y += 50

        # Draw biome details if one is selected
        if biome_list:
            curr_biome = biome_list[self.gs.selected_biome_index]
            details = biomes.get(curr_biome, {})
            margin = 50
            bottom_y = 400
            desc = details.get("description", "No description available.")
            draw_wrapped_text(screen, font, f"Description: {desc}", margin, bottom_y+50, window_width - margin*2)
            pygame.draw.rect(screen, (50,50,50), (window_width//2, 100, 350, 250))
            draw_text(screen, font, "Biome Image", (window_width//2+ (350//3-3)), 170)

        # Draw and store confirm/back button rectangles
        is_hovered, self.confirm_button_rect = draw_button(screen, font, "Confirm", window_width//2 - 100, window_height - 60, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        is_hovered, self.back_button_rect = draw_button(screen, font, "Back", window_width//2 + 120, window_height - 60, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)

        pygame.display.flip()

class PauseScreen(Screen):
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                safe_quit()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_p:
                    self.gs.current_state = "gameplay"
            elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                hovered_resume, _ = draw_button(screen, font, "Resume", window_width//2, window_height//2 - BUTTON_SPACING, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
                if hovered_resume:
                    self.gs.current_state = "gameplay"
                else:
                    hovered_menu, _ = draw_button(screen, font, "Main Menu", window_width//2, window_height//2 + BUTTON_SPACING, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
                    if hovered_menu:
                        self.gs.current_state = "start_menu"
        pygame.time.wait(100)

    def update(self, dt: float):
        pass

    def render(self, screen: pygame.Surface):
        screen.fill((0,0,0,180))
        draw_titletext(screen, font, "Paused", window_width//2, window_height//2 - 100)
        draw_button(screen, font, "Resume", window_width//2, window_height//2 - BUTTON_SPACING, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        draw_button(screen, font, "Main Menu", window_width//2, window_height//2 + BUTTON_SPACING, None, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        pygame.display.flip()

class InventoryScreen(Screen):
    def __init__(self, gs):
        super().__init__(gs)
        self.selected_item = None
        self.scroll_offset = 0
        self.items_per_row = 5
        self.item_size = ITEM_SIZE
        self.padding = ITEM_PADDING

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                safe_quit()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_i:
                    self.gs.current_state = "gameplay"
                elif event.key == pygame.K_ESCAPE:
                    self.gs.current_state = "gameplay"
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    mouse_pos = pygame.mouse.get_pos()
                    # Check inventory slots
                    player_entity = ecs_world.get_player_entity()
                    inv_comp = player_entity.get_component(InventoryComponent) if player_entity else None
                    if inv_comp:
                        start_x = GRID_START_X
                        start_y = GRID_START_Y
                        for i, (item_name, qty) in enumerate(inv_comp.items.items()):
                            row = i // self.items_per_row
                            col = i % self.items_per_row
                            x = start_x + col * (self.item_size + self.padding)
                            y = start_y + row * (self.item_size + self.padding)
                            rect = pygame.Rect(x, y, self.item_size, self.item_size)
                            if rect.collidepoint(mouse_pos):
                                self.selected_item = item_name
                                break
                            # Check close button
                            close_rect = pygame.Rect(window_width//2 - 50, window_height-70, 100, 40)
                            if close_rect.collidepoint(mouse_pos):
                                self.gs.current_state = "gameplay"
                elif event.button == 4:  # Mouse wheel up
                    self.scroll_offset = max(0, self.scroll_offset - 20)
                elif event.button == 5:  # Mouse wheel down
                    self.scroll_offset += 20  # We'll clamp this in render based on content height

    def render(self, screen: pygame.Surface):
        screen.fill((20,20,20))
        draw_titletext(screen, font, "Inventory", window_width//2, 50)

        # Get player components
        player_entity = ecs_world.get_player_entity()
        inv_comp = player_entity.get_component(InventoryComponent) if player_entity else None
        skill_comp = player_entity.get_component(GatheringSkillComponent) if player_entity else None

        if inv_comp:
            # Draw inventory grid
            start_x = GRID_START_X
            start_y = GRID_START_Y
            for i, (item_name, qty) in enumerate(inv_comp.items.items()):
                row = i // self.items_per_row
                col = i % self.items_per_row
                x = start_x + col * (self.item_size + self.padding)
                y = start_y + row * (self.item_size + self.padding)

                # Draw slot background
                color = (100,100,100) if item_name == self.selected_item else (80,80,80)
                pygame.draw.rect(screen, color, (x, y, self.item_size, self.item_size))

                # Draw item name and quantity
                name_short = item_name[:10] + ".." if len(item_name) > 10 else item_name
                draw_text(screen, font, name_short, x + 5, y + 10)
                draw_text(screen, font, f"x{qty}", x + 5, y + 35)

            # Draw detailed item info if an item is selected
            if self.selected_item:
                item_data = data.get("items", {}).get(self.selected_item, {})
                
                # Calculate safe position for item details
                detail_width = 330
                detail_height = 300
                detail_x = window_width - detail_width - 20  # 20 pixels padding
                detail_y = 100  # Fixed Y position
                
                draw_item_details(screen, font, self.selected_item, item_data,
                                detail_x, detail_y, detail_width, detail_height)

        # Draw gathering skills
        if skill_comp:
            # Calculate safe position for skills
            skill_width = 300
            skill_height = len(skill_comp.skills) * 30 + 40  # 30 per skill + 10 padding
            skill_x = 50  # Fixed X position
            skill_y = window_height - skill_height - 40  # Fixed Y position from bottom
                
            draw_text(screen, font, "Gathering Skills:", skill_x, skill_y - 30)
            for i, (skill_name, level) in enumerate(skill_comp.skills.items()):
                y = skill_y + i * 30
                xp = skill_comp.xp[skill_name]
                threshold = skill_comp.xp_threshold
                draw_skill_bar(screen, font, skill_name, level, xp, threshold,
                             skill_x, y, width=skill_width)

        # Draw close button with safe positioning
        button_width = 100
        button_height = font.get_height() + 20
        button_x, button_y, _ = get_safe_position(screen, 
            window_width//2 - button_width//2, window_height - button_height - 20, 
            button_width, button_height, 
            padding=20)
        
        is_hovered, button_rect = draw_button(screen, font, "Close", button_x, button_y,
                   button_width, BUTTON_FONT_SIZE, BUTTON_COLOR, BUTTON_HOVER_COLOR)
        self.close_button_rect = pygame.Rect(button_rect)

        pygame.display.flip()

class GameplayScreen(Screen):
    def __init__(self, gs):
        super().__init__(gs)
        self.interaction_cooldown = 0.5
        self.last_interaction_time = 0

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                safe_quit()
            elif event.type == pygame.VIDEORESIZE:
                handle_resize(event)
            elif event.type == pygame.KEYDOWN:
                control = get_control_from_event(event)
                if control == 'fullscreen':
                    toggle_fullscreen(self.gs)
                elif control == 'pause':
                    self.gs.current_state = "pause"
                elif control == 'open_inventory':
                    self.gs.current_state = "inventory"
                elif control == 'open_crafting':
                    self.gs.current_state = "crafting"
                elif control == 'attack':
                    now = time.time()
                    if now - self.gs.last_attack_time >= 1.0:
                        for sys in ecs_world.systems:
                            if isinstance(sys, CombatSystem):
                                sys.player_initiate_attack()
                                self.show_feedback_message("Attack successful!", color=(255, 100, 100))
                                break
                        self.gs.last_attack_time = now
                elif control == 'gather':
                    now = time.time()
                    if now - self.last_interaction_time >= self.interaction_cooldown:
                        self.interact_with_tile()
                        self.show_feedback_message("Gathered resource!", color=(100, 255, 100))
                        self.last_interaction_time = now
                elif control == 'interact':
                    now = time.time()
                    if now - self.last_interaction_time >= self.interaction_cooldown:
                        self.interact_with_tile()
                        self.last_interaction_time = now
                elif control == 'toggle_debug_overlay':
                    self.gs.debug_overlay = not self.gs.debug_overlay
                elif control == 'open_world_editor':
                    # Launch the world editor in a separate process
                    import subprocess
                    import os
                    try:
                        # Get the current directory
                        current_dir = os.getcwd()
                        # Launch the world editor
                        subprocess.Popen(['python', 'src/world_editor.py'], cwd=current_dir)
                        self.show_feedback_message("Launching World Editor...", color=(100, 255, 100))
                    except Exception as e:
                        self.show_feedback_message(f"Error launching World Editor: {e}", color=(255, 100, 100))

    def update(self, dt: float):
        # Get keyboard state for smoother movement
        keys = pygame.key.get_pressed()
        dx = dy = 0

        # Calculate movement vector
        if keys[pygame.K_UP]:
            dy -= 1
        if keys[pygame.K_DOWN]:
            dy += 1
        if keys[pygame.K_LEFT]:
            dx -= 1
        if keys[pygame.K_RIGHT]:
            dx += 1

        # Normalize diagonal movement
        if dx != 0 and dy != 0:
            dx *= 0.707  # 1/√2
            dy *= 0.707

        # Apply movement
        old_x, old_y = self.gs.player_x, self.gs.player_y
        self.gs.player_x += dx * player_speed * dt
        self.gs.player_y += dy * player_speed * dt

        # Get world boundaries from current active region size
        if world_manager and world_manager.terrain_generator:
            region_size = world_manager.terrain_generator.region_size
            region_grid_size = world_manager.terrain_generator.region_grid_size
            max_x = region_grid_size * region_size - 1
            max_y = region_grid_size * region_size - 1
            self.gs.player_x = max(0.0, min(self.gs.player_x, float(max_x)))
            self.gs.player_y = max(0.0, min(self.gs.player_y, float(max_y)))

            # Update active regions if player position changed
            if (old_x != self.gs.player_x or old_y != self.gs.player_y) and world_manager:
                world_manager.update_active_regions(self.gs.player_x, self.gs.player_y)

        # Update world time and systems
        now = time.time()
        if now - self.gs.last_ore_gather >= data.get("npc_ore_gather_interval", 1.0):
            self.gs.npc_ore_count += 1
            self.gs.last_ore_gather = now
            

        if world_manager:
            season = world_manager.get_current_season_and_year()
            current_weather = world_manager.get_current_weather()
            ecs_world.update(dt=dt * 0.1, current_season=season, current_weather=current_weather)

        # Update camera position
        cam_comp = camera_entity.get_component(CameraComponent)
        if cam_comp:
            max_cx = max_x - cam_comp.width
            max_cy = max_y - cam_comp.height
            cam_comp.x = max(0.0, min(self.gs.player_x - cam_comp.width / 2.0, float(max_cx)))
            cam_comp.y = max(0.0, min(self.gs.player_y - cam_comp.height / 2.0, float(max_cy)))

    def interact_with_tile(self):
        """Handle interaction with the current tile"""
        
        current_tile = get_current_tile(self.gs)
        
        if not current_tile:
            
            return
        # Get required components
        player = ecs_world.get_player_entity()
        
        if not player:
            
            return
        inv_comp = player.get_component(InventoryComponent)
        tool_comp = player.get_component(ToolComponent)
        skill_comp = player.get_component(GatheringSkillComponent)
        
        
        
        current_time = time.time()
        resources = current_tile.get("resources", {})
        
        if resources:
            gathering_system = None
            for system in ecs_world.systems:
                if isinstance(system, GatheringSystem):
                    gathering_system = system
                    break
            
            if gathering_system:
                for resource, details in resources.items():
                    if details.get("quantity", 0) > 0:
                        
                        result = gathering_system.gather_resource(player, resource, details, current_time)
                        
                        if result.success:
                            details["quantity"] -= 1  # Reduce resource in tile
                            self.show_feedback_message(result.message, (0, 255, 0))
                            self.show_gathering_effect(resource)
                            if result.tool_broke:
                                self.show_feedback_message("Tool broke!", (255, 0, 0))
                        else:
                            self.show_feedback_message(result.message, (255, 255, 0))
                        break  # Only gather one resource at a time
        feature = current_tile.get("terrain_feature")
        if feature:
            self.show_feedback_message(f"Found {feature}")

    def show_gathering_effect(self, resource_type: str):
        """Show visual feedback for gathering success"""
        # Get the current tile's screen position
        cam_comp = camera_entity.get_component(CameraComponent)
        if not cam_comp:
            return

        # Calculate screen coordinates
        cam_tile_x = int(cam_comp.x)
        cam_tile_y = int(cam_comp.y)
        offx = (cam_comp.x - cam_tile_x) * tile_size
        offy = (cam_comp.y - cam_tile_y) * tile_size

        # Get player's screen position
        pxs = int((self.gs.player_x - cam_tile_x) * tile_size - offx)
        pys = int((self.gs.player_y - cam_tile_y) * tile_size - offy)

        # Choose effect color based on resource type
        colors = {
            "wood": (139, 69, 19),      # Brown
            "stone": (169, 169, 169),    # Gray
            "iron_ore": (176, 141, 87),  # Bronze
            "herbs": (0, 255, 0),        # Green
            "rare_crystals": (148, 0, 211),  # Purple
            "gold": (255, 215, 0)        # Gold
        }
        color = colors.get(resource_type, (255, 255, 255))

        # Create expanding circle effect at gathering location
        for size in range(10, 40, 2):
            screen_copy = screen.copy()
            draw_gathering_effect(screen_copy, pxs + tile_size//2, pys + tile_size//2,
                                resource_type, color, size)
            screen.blit(screen_copy, (0,0))
            pygame.display.flip()
            pygame.time.wait(10)  # Short delay for animation

    def render(self, screen: pygame.Surface):
        draw_gameplay(self.gs, dt=clock.get_time()/1000.0)

        # Update and draw feedback messages
        remaining_messages = []
        
        # Calculate safe message area
        message_area_height = window_height * 0.3  # Use 30% of screen height for messages
        message_area_y = window_height - message_area_height
        
        # Calculate available space for messages
        message_height = font.get_height() + 10
        max_messages = int(message_area_height / message_height)
        
        # Draw messages from bottom up within safe area
        current_y = message_area_y + message_area_height - message_height
        for msg, duration, max_duration, color in self.gs.feedback_messages[:max_messages]:
            if duration > 0:
                # Ensure message stays within bounds
                message_x = 10
                message_y = min(current_y, window_height - message_height)
                draw_popup_message(screen, font, msg, message_x, message_y, duration, max_duration, color)
                remaining_messages.append((msg, duration - clock.get_time()/1000.0, max_duration, color))
                current_y -= message_height
                if current_y < message_area_y:
                    break  # Stop if we reach the top of the message area

        self.gs.feedback_messages = remaining_messages

        # Draw tool durability bars
        player = ecs_world.get_player_entity()
        if player:
            tool_comp = player.get_component(ToolComponent)
            if tool_comp:
                # Calculate space needed for all tools
                tool_count = len(tool_comp.equipped_tools)
                tool_height = 40
                total_height = tool_count * tool_height
                
                # Get safe position for tools
                tool_x = window_width - 150
                tool_y = 50
                
                # Ensure tools don't overlap with other UI elements
                tool_x, tool_y, _ = get_safe_position(screen, 
                    tool_x, tool_y, 
                    200, total_height,  # 200 is arbitrary width for tool bars
                    padding=20)
                
                for tool_name, tool_data in tool_comp.equipped_tools.items():
                    draw_tool_durability(screen, font, tool_name,
                                       tool_data["durability"],
                                       tool_data["max_durability"],
                                       tool_x, tool_y)
                    tool_y += tool_height

        # Draw mini-map in the top-right corner
        if world_manager and world_manager.world_map:
            # Mini-map configuration
            mini_map_size = 150
            mini_map_x = window_width - mini_map_size - 10
            mini_map_y = 10
            mini_map_border = 2
            mini_map_view_radius = 20  # How many tiles to show around the player

            # Get player position
            player_x = int(self.gs.player_x)
            player_y = int(self.gs.player_y)

            # Only update the mini-map when player position changes or every 30 frames
            # This prevents constant redrawing which causes flickering
            should_update_minimap = False

            # Initialize mini_map_surf and last_player_pos if they don't exist
            if not hasattr(self, 'mini_map_surf') or not hasattr(self, 'last_player_pos'):
                self.mini_map_surf = pygame.Surface((mini_map_size, mini_map_size))
                self.last_player_pos = (None, None)
                should_update_minimap = True

            # Update mini-map only when needed
            if should_update_minimap:
                self.mini_map_surf.fill((30, 30, 30))

                # Calculate mini-map scale
                mini_map_scale = mini_map_size / (mini_map_view_radius * 2)

                # Draw tiles on mini-map
                for y in range(player_y - mini_map_view_radius, player_y + mini_map_view_radius):
                    for x in range(player_x - mini_map_view_radius, player_x + mini_map_view_radius):
                        tile = world_manager.get_tile_global(x, y)
                        if tile:
                            # Calculate position on mini-map
                            mx = int((x - (player_x - mini_map_view_radius)) * mini_map_scale)
                            my = int((y - (player_y - mini_map_view_radius)) * mini_map_scale)

                            # Get tile color
                            biome = tile.get("biome", "Unknown")
                            if biome == "Ocean":
                                hex_col = color_palette.get("water", "#0077BE")
                            elif biome == "Molten Fields":
                                hex_col = color_palette.get("lava", "#FF4500")
                            else:
                                hex_col = color_palette.get("biomes", {}).get(biome, "#808080")

                            # Convert hex to RGB
                            try:
                                color = tuple(int(hex_col.strip('#')[i:i+2], 16) for i in (0, 2, 4))
                            except (ValueError, IndexError):
                                color = (128, 128, 128)  # Fallback gray

                            # Draw tile on mini-map
                            tile_size = max(1, int(mini_map_scale))
                            pygame.draw.rect(self.mini_map_surf, color, (mx, my, tile_size, tile_size))

                            # Mark resources with yellow dots
                            if tile.get("resources"):
                                pygame.draw.circle(self.mini_map_surf, (255, 215, 0),
                                                 (mx + tile_size // 2, my + tile_size // 2),
                                                 max(1, tile_size // 2))

                            # Draw mountain peaks for high elevation areas
                            elevation = tile.get("elevation", 0.5)
                            if elevation > 0.7 and biome != "Ocean" and tile_size >= 6:
                                mountain_color = (min(255, color[0] + 40),
                                                min(255, color[1] + 40),
                                                min(255, color[2] + 40))
                                # Draw a triangle for mountains
                                peak_height = max(2, tile_size//2)
                                points = [
                                    (mx + tile_size//2,
                                     my + tile_size//4),
                                    (mx + tile_size//4,
                                     my + tile_size//4 + peak_height),
                                    (mx + tile_size//4 + tile_size//2,
                                     my + tile_size//4 + peak_height)
                                ]
                                pygame.draw.polygon(self.mini_map_surf, mountain_color, points)

                            # Draw settlement marker if available
                            settlement_pos = self.gs.settlement_pos
                            if settlement_pos:
                                sx, sy = settlement_pos
                                if sx == x and sy == y:
                                    # Draw settlement marker
                                    settlement_color = data.get("color_palette", {}).get("settlements", {}).get("Medium Settlement", "#FFD700")
                                    try:
                                        color = tuple(int(settlement_color.strip('#')[i:i+2], 16) for i in (0, 2, 4))
                                    except (ValueError, IndexError):
                                        color = (255, 215, 0)  # Gold fallback

                                    # Calculate center position
                                    center_x = mx + tile_size // 2
                                    center_y = my + tile_size // 2
                                    # Draw the settlement marker
                                    pygame.draw.circle(self.mini_map_surf, color, (center_x, center_y), max(2, tile_size // 4))

                # Draw player position on mini-map (center)
                player_mini_x = mini_map_size // 2
                player_mini_y = mini_map_size // 2
                pygame.draw.rect(self.mini_map_surf, (255, 255, 255),
                               (player_mini_x - 2, player_mini_y - 2, 4, 4))

            # Draw mini-map border
            pygame.draw.rect(screen, (100, 100, 100),
                           (mini_map_x - mini_map_border, mini_map_y - mini_map_border,
                            mini_map_size + mini_map_border * 2, mini_map_size + mini_map_border * 2))

            # Draw mini-map on screen
            screen.blit(self.mini_map_surf, (mini_map_x, mini_map_y))

            # Draw mini-map label
            small_font = pygame.font.SysFont(None, 20)
            label = small_font.render("Mini-Map", True, (255, 255, 255))
            screen.blit(label, (mini_map_x + (mini_map_size - label.get_width()) // 2,
                              mini_map_y + mini_map_size + 5))

        pygame.display.flip()

def draw_gameplay(gs: GameState, dt: float):
    try:
        screen.fill((30,30,30))
    except pygame.error:
        pass
    cam_comp = camera_entity.get_component(CameraComponent)
    if not cam_comp or not world_manager:
        pygame.display.flip()
        return
    # --- DEBUG: Keypress to export biome grid ---
    if is_control_pressed('debug_export_biome_grid'):
        export_biome_grid_debug()
    cam_tile_x = int(cam_comp.x)
    cam_tile_y = int(cam_comp.y)
    offx = (cam_comp.x - cam_tile_x) * tile_size
    offy = (cam_comp.y - cam_tile_y) * tile_size
    visx = cam_comp.width + 1
    visy = cam_comp.height + 1

    # Draw world tiles from loaded regions with enhanced visualization
    for y in range(visy):
        for x in range(visx):
            gx = cam_tile_x + x
            gy = cam_tile_y + y
            tile = world_manager.get_tile_global(gx, gy) if world_manager else None
            if not tile:
                continue

            # Get tile properties
            biome = tile.get("biome", "Unknown")
            elevation = tile.get("elevation", 0.5)
            temperature = tile.get("temperature", 0)
            rainfall = tile.get("rainfall", 0)

            # Get base color for the biome
            if biome == "Ocean":
                hex_col = color_palette.get("water", "#0077BE")
            elif biome == "Molten Fields":
                hex_col = color_palette.get("lava", "#FF4500")
            else:
                hex_col = color_palette.get("biomes", {}).get(biome, "#808080")

            # Convert hex to RGB
            try:
                base_color = tuple(int(hex_col.strip('#')[i:i+2], 16) for i in (0, 2, 4))
            except (ValueError, IndexError):
                base_color = (128, 128, 128)  # Fallback gray

            # Adjust color based on elevation for more visual depth
            brightness_factor = 0.7 + (elevation * 0.6)  # 0.7 to 1.3 range
            adjusted_color = (
                min(255, int(base_color[0] * brightness_factor)),
                min(255, int(base_color[1] * brightness_factor)),
                min(255, int(base_color[2] * brightness_factor))
            )

            # Calculate screen position
            sx = int(x * tile_size - offx)
            sy = int(y * tile_size - offy)

            # Draw the tile with adjusted color
            pygame.draw.rect(screen, adjusted_color, (sx, sy, tile_size, tile_size))

            # Add terrain features based on biome and environmental factors
            if biome == "Ocean" and tile_size >= 6:
                # Draw wave patterns for ocean
                wave_color = (max(0, adjusted_color[0] - 30),
                             max(0, adjusted_color[1] - 30),
                             max(0, adjusted_color[2] - 30))
                pygame.draw.line(screen, wave_color,
                               (sx + tile_size//4, sy + tile_size//2),
                               (sx + tile_size//2, sy + tile_size//3),
                               max(1, tile_size//8))

            elif biome in ["Verdant Forest", "Twilight Marsh"] and tile_size >= 6:
                # Draw tree/vegetation dots
                tree_color = (max(0, adjusted_color[0] - 40),
                             min(255, adjusted_color[1] + 20),
                             max(0, adjusted_color[2] - 40))
                pygame.draw.circle(screen, tree_color,
                                 (sx + tile_size//2, sy + tile_size//2),
                                 max(1, tile_size//4))

            elif biome in ["Molten Fields"] and tile_size >= 6:
                # Draw lava/heat patterns
                lava_color = (min(255, adjusted_color[0] + 60),
                             max(0, adjusted_color[1] - 20),
                             max(0, adjusted_color[2] - 30))
                pygame.draw.circle(screen, lava_color,
                                 (sx + tile_size//2, sy + tile_size//2),
                                 max(1, tile_size//5))

            elif biome in ["Frost-Hewn Tundra"] and tile_size >= 6:
                # Draw snow/ice patterns
                snow_color = (min(255, adjusted_color[0] + 30),
                             min(255, adjusted_color[1] + 30),
                             max(255, adjusted_color[2] + 30))
                pygame.draw.circle(screen, snow_color,
                                 (sx + tile_size//2, sy + tile_size//2),
                                 max(1, tile_size//5))

            # In normal view, just show a resource indicator
            center_x = sx + tile_size // 2
            center_y = sy + tile_size // 2
            radius = max(2, int(tile_size // 8))
            pygame.draw.circle(screen, (255, 215, 0), (center_x, center_y), radius)
    player_color_hex = color_palette.get("player", "#808080")
    player_color = tuple(int(player_color_hex.strip('#')[i:i+2], 16) for i in (0,2,4))

    # Calculate player position with floating-point precision
    pxs = (gs.player_x - cam_tile_x) * tile_size - offx
    pys = (gs.player_y - cam_tile_y) * tile_size - offy

    # Draw player as a slightly smaller rectangle centered in the tile
    player_size = int(tile_size * 0.8)  # 80% of tile size
    player_offset = (tile_size - player_size) // 2

    # Draw player with a border
    pygame.draw.rect(screen, player_color, (
        int(pxs) + player_offset,
        int(pys) + player_offset,
        player_size,
        player_size
    ))
    pygame.draw.rect(screen, (255, 255, 255), (
        int(pxs) + player_offset,
        int(pys) + player_offset,
        player_size,
        player_size
    ), 2)  # 2-pixel white border
    current_tile = get_current_tile(gs)
    if current_tile:
        player_tile_x = int(gs.player_x)
        player_tile_y = int(gs.player_y)
        sx = int((player_tile_x - cam_tile_x) * tile_size - offx)
        sy = int((player_tile_y - cam_tile_y) * tile_size - offy)
        pygame.draw.rect(screen, (255,255,255), (sx, sy, tile_size, tile_size), 2)
    companion_color = get_fallback_color("companion")
    cxs = int((gs.companion_x - cam_tile_x) * tile_size - offx)
    cys = int((gs.companion_y - cam_tile_y) * tile_size - offy)
    pygame.draw.rect(screen, companion_color, (cxs, cys, tile_size, tile_size))
    threat_colors = data.get("threat_colors", {"Low": "#00C8C8", "Medium": "#C8C800", "High": "#C80000", "Default": "#C8C8C8"})
    for entity, (pos_comp, cre_comp) in ecs_world.get_entities_with(PositionComponent, CreatureComponent):
        c_hex = threat_colors.get(cre_comp.threat_level, threat_colors["Default"])
        ccol = tuple(int(c_hex.strip('#')[i:i+2], 16) for i in (0,2,4))
        sx = int((pos_comp.x - cam_tile_x) * tile_size - offx)
        sy = int((pos_comp.y - cam_tile_y) * tile_size - offy)
        pygame.draw.rect(screen, ccol, (sx, sy, tile_size, tile_size))
    settlement_color = get_fallback_color("settlement")
    for entity, (sett, inv) in ecs_world.get_entities_with(SettlementComponent, InventoryComponent):
        sx = int((sett.x - cam_tile_x) * tile_size - offx)
        sy = int((sett.y - cam_tile_y) * tile_size - offy)
        pygame.draw.rect(screen, settlement_color, (sx, sy, tile_size, tile_size))
        draw_text(screen, font, "S", sx + tile_size//4, sy + tile_size//4)
    player_ent = ecs_world.get_player_entity()
    st_c = player_ent.get_component(StatsComponent) if player_ent else None
    if st_c:
        draw_text(screen, font, f"HP: {st_c.hp}/{st_c.max_hp}", 10, window_height - 180)
        draw_text(screen, font, f"MP: {st_c.mp}/{st_c.max_mp}", 10, window_height - 150)
        draw_text(screen, font, f"Stamina: {st_c.stamina}/{st_c.max_stamina}", 10, window_height - 120)
    draw_text(screen, font, f"Pos: ({int(gs.player_x)}, {int(gs.player_y)})", 10, window_height - 90)
    draw_text(screen, font, f"Race: {gs.player_race_name}", 10, window_height - 60)
    draw_text(screen, font, f"Biome: {gs.player_biome_name}", 10, window_height - 30)
    season, current_year, season_data, day_in_season = world_manager.get_current_season_and_year() if world_manager else ("Spring", 1, {}, 1)
    hour = int(ecs_world.day_time) % 24
    am_pm = "am" if hour < 12 else "pm"
    hour_display = hour if hour <= 12 else hour - 12
    time_str = f"Day {day_in_season} {hour_display}:00 {am_pm}"
    draw_text(screen, font, time_str, 200, window_height - 90)
    cs, _, _, _ = world_manager.get_current_season_and_year() if world_manager else ("Spring",1,{},1)
    cw = world_manager.get_current_weather() if world_manager else "Clear"
    draw_text(screen, font, f"Season: {cs}", 200, window_height - 60)
    draw_text(screen, font, f"Weather: {cw}", 200, window_height - 30)
    draw_tile_info(screen, font, current_tile, data, window_width, window_height)

    # Draw weather effects
    if world_manager:
        current_weather = world_manager.get_current_weather()

        # Apply weather visual effects
        if current_weather == "Rain":
            # Draw rain particles
            for _ in range(100):
                rain_x = random.randint(0, window_width)
                rain_y = random.randint(0, window_height)
                rain_length = random.randint(5, 15)
                pygame.draw.line(screen, (200, 200, 255),
                               (rain_x, rain_y),
                               (rain_x - 2, rain_y + rain_length),
                               1)

        elif current_weather == "Snow":
            # Draw snow particles
            for _ in range(80):
                snow_x = random.randint(0, window_width)
                snow_y = random.randint(0, window_height)
                snow_size = random.randint(1, 3)
                pygame.draw.circle(screen, (255, 255, 255),
                                 (snow_x, snow_y),
                                 snow_size)

        elif current_weather == "Storm":
            # Draw storm effects (rain + occasional lightning)
            for _ in range(150):
                rain_x = random.randint(0, window_width)
                rain_y = random.randint(0, window_height)
                rain_length = random.randint(10, 20)
                pygame.draw.line(screen, (180, 180, 240),
                               (rain_x, rain_y),
                               (rain_x - 4, rain_y + rain_length),
                               2)

            # Occasional lightning flash
            if random.random() < 0.02:  # 2% chance per frame
                lightning_surface = pygame.Surface((window_width, window_height), pygame.SRCALPHA)
                lightning_surface.fill((255, 255, 255, 50))  # Semi-transparent white
                screen.blit(lightning_surface, (0, 0))

        elif current_weather == "Fog":
            # Draw fog effect
            fog_surface = pygame.Surface((window_width, window_height), pygame.SRCALPHA)
            fog_surface.fill((200, 200, 200, 100))  # Semi-transparent gray
            screen.blit(fog_surface, (0, 0))

        elif current_weather == "Sandstorm" and gs.player_biome_name == "Golden Sands Desert":
            # Draw sandstorm particles
            for _ in range(120):
                sand_x = random.randint(0, window_width)
                sand_y = random.randint(0, window_height)
                sand_size = random.randint(1, 2)
                pygame.draw.circle(screen, (194, 178, 128),
                                 (sand_x, sand_y),
                                 sand_size)

            # Add sand overlay
            sand_surface = pygame.Surface((window_width, window_height), pygame.SRCALPHA)
            sand_surface.fill((194, 178, 128, 70))  # Semi-transparent sand color
            screen.blit(sand_surface, (0, 0))

    # Debug overlay: show chunk boundaries, resource clusters, and detailed tile info
    if gs.debug_overlay and world_manager and world_manager.terrain_generator:
        region_size = world_manager.terrain_generator.region_size
        grid_size = world_manager.terrain_generator.region_grid_size
        
        # Calculate visible tile range for optimization
        start_tile_x = max(0, int(cam_tile_x - 1))
        end_tile_x = min(len(world_manager.world_map.get("tiles", [])), int(cam_tile_x + (window_width // tile_size) + 2))
        start_tile_y = max(0, int(cam_tile_y - 1))
        end_tile_y = min(len(world_manager.world_map.get("tiles", [])), int(cam_tile_y + (window_height // tile_size) + 2))
        
        # Cache fonts and surfaces to avoid recreating them
        if not hasattr(draw_gameplay, 'debug_font'):
            draw_gameplay.debug_font = pygame.font.SysFont(None, max(8, int(tile_size // 5)))
        small_font = draw_gameplay.debug_font
        
        # Draw region grid boundaries (only visible regions)
        region_start_x = max(0, int(cam_tile_x // region_size) - 1)
        region_end_x = min(grid_size, int((cam_tile_x + (window_width // tile_size)) // region_size) + 2)
        region_start_y = max(0, int(cam_tile_y // region_size) - 1)
        region_end_y = min(grid_size, int((cam_tile_y + (window_height // tile_size)) // region_size) + 2)
        
        # Draw vertical grid lines
        for i in range(region_start_x, region_end_x + 1):
            x_pos = int((i * region_size - cam_tile_x) * tile_size - offx)
            if -tile_size <= x_pos <= window_width + tile_size:
                pygame.draw.line(screen, (255, 0, 0, 100), (x_pos, 0), (x_pos, window_height), 1)
        
        # Draw horizontal grid lines
        for i in range(region_start_y, region_end_y + 1):
            y_pos = int((i * region_size - cam_tile_y) * tile_size - offy)
            if -tile_size <= y_pos <= window_height + tile_size:
                pygame.draw.line(screen, (255, 0, 0, 100), (0, y_pos), (window_width, y_pos), 1)

        # Only process visible tiles
        for dy in range(start_tile_y, end_tile_y):
            if dy >= len(world_manager.world_map.get("tiles", [])):
                continue
                
            row = world_manager.world_map["tiles"][dy]
            for dx in range(start_tile_x, end_tile_x):
                if dx >= len(row):
                    continue
                    
                tile = row[dx]
                sx = int((dx - cam_tile_x) * tile_size - offx)
                sy = int((dy - cam_tile_y) * tile_size - offy)

                # Skip if not visible on screen
                if sx < -tile_size or sy < -tile_size or sx > window_width or sy > window_height:
                    continue

                # Highlight resources with green circles (only if tile has resources)
                if tile.get("resources"):
                    center_x = sx + tile_size // 2
                    center_y = sy + tile_size // 2
                    pygame.draw.circle(screen, (0, 255, 0, 180), (center_x, center_y), 5)


                # Draw detailed tile information if zoomed in enough
                if tile_size >= 30:
                    # Only render text for every other tile when zoomed in to reduce clutter
                    if (dx + dy) % 2 == 0:
                        # Draw coordinates
                        coord_text = f"{dx},{dy}"
                        txt_surf = small_font.render(coord_text, True, (255, 255, 255, 200))
                        screen.blit(txt_surf, (sx + 2, sy + 2))
                        
                        # Draw biome name (first 3 chars) if there's space
                        if tile_size > 40:
                            biome = tile.get("biome", "?")
                            if biome != "?":
                                biome = biome[:3]
                            txt_surf = small_font.render(biome, True, (200, 200, 255, 200))
                            screen.blit(txt_surf, (sx + 2, sy + tile_size - 14))
                            
                            # Draw elevation if space allows
                            if tile_size > 60 and "elevation" in tile:
                                elev_text = f"{tile['elevation']:.0f}"
                                txt_surf = small_font.render(elev_text, True, (255, 200, 200, 200))
                                screen.blit(txt_surf, (sx + 2, sy + tile_size // 2))

                        if "temperature" in tile:
                            temp_text = f"T:{tile['temperature']:.1f}°C"
                            txt_surf = small_font.render(temp_text, True, (255, 255, 255))
                            screen.blit(txt_surf, (sx + 2, sy + tile_size // 2))

                        if "rainfall" in tile:
                            rain_text = f"R:{tile['rainfall']:.1f}mm"
                            txt_surf = small_font.render(rain_text, True, (255, 255, 255))
                            screen.blit(txt_surf, (sx + 2, sy + 2 * tile_size // 3))

        # Draw current player tile highlight
        player_tile_x = int(gs.player_x)
        player_tile_y = int(gs.player_y)
        sx = int((player_tile_x - cam_tile_x) * tile_size - offx)
        sy = int((player_tile_y - cam_tile_y) * tile_size - offy)
        pygame.draw.rect(screen, (255, 255, 0), (sx, sy, tile_size, tile_size), 2)
    pygame.display.flip()

# -------------------------
# Central Game Application Class
# -------------------------
from screens.crafting_screen import CraftingScreen
from screens.loading_screen import LoadingScreen

class GameApp:
    def __init__(self):
        self.gs = GameState()
        self.screens = {
            "start_menu": StartMenuScreen(self.gs),
            "character_selection": CharacterSelectionScreen(self.gs),
            "biome_selection": BiomeSelectionScreen(self.gs),
            "seed_selection": SeedSelectionScreen(self.gs),
            "loading": LoadingScreen(self.gs, world_manager, ecs_world, safe_quit, font, window_width, window_height),
            "gameplay": GameplayScreen(self.gs),
            "pause": PauseScreen(self.gs),
            "inventory": InventoryScreen(self.gs),
            "crafting": CraftingScreen(self.gs),
        }

    def run(self) -> None:
        """Run the main game loop.

        This method handles the main game loop, managing screen updates,
        event handling, and rendering.
        """
        try:
            while True:
                dt = clock.tick(60) / 1000.0
                current_screen = self.screens[self.gs.current_state]
                current_screen.handle_events()
                current_screen.update(dt)
                current_screen.render(screen)
                pygame.display.flip()
        except Exception as e:
            logging.error(f"Error in game loop: {str(e)}")
            raise

if __name__ == "__main__":
    """Entry point for the game application."""
    try:
        app = GameApp()
        app.run()
    except Exception as e:
        logging.error(f"Game initialization failed: {str(e)}")
        raise