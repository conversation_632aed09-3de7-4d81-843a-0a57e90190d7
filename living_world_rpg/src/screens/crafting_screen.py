"""
CraftingScreen for Living World RPG
Allows players to view recipes and craft items.
"""

# Standard library imports
import os
from src.typing import Any, Dict, List, Optional, Tuple, Union, Type, TypeVar

# Third-party imports
import pygame

# Local application imports
from src.controls import get_control_from_event
from src.core.components import InventoryComponent
from src.ui_util import draw_button, draw_text

class CraftingScreen:
    def __init__(self, gs):
        import main
        self.gs = gs
        self.data = main.data
        self.recipes = self.data.get("crafting_recipes", {}).get("recipes", [])
        self.scroll_offset = 0
        self.recipe_button_rects = []
        self.craft_button_rect = None
        self.back_button_rect = None
        self.hovered_index = None
        self.font = pygame.font.Font(None, 30)
        self.small_font = pygame.font.Font(None, 24)

        # Item categories and filtering
        self.categories = ["All", "Weapons", "Tools", "Armor", "Potions", "Resources", "Food"]
        self.selected_category = "All"
        self.category_buttons = []
        self.filtered_recipes = self.recipes.copy()

        # Load item icons for recipes
        asset_dir = os.path.normpath(os.path.join(os.path.dirname(__file__), '..', 'assets'))
        self.item_icons = {}
        self.items_data = self.data.get("items", {})
        # Build lowercase mapping for item data
        self.items_data_lower = {k.lower(): v for k, v in self.items_data.items()}

        # Load all .png icons in assets directory, map by lowercase name
        for fname in os.listdir(asset_dir):
            if fname.lower().endswith('.png'):
                item_key = fname[:-4].lower()  # Remove .png extension and lowercase
                img_path = os.path.join(asset_dir, fname)
                try:
                    img = pygame.image.load(img_path).convert_alpha()
                    self.item_icons[item_key] = pygame.transform.scale(img, (32, 32))
                except Exception:
                    pass

        # Create default icon for items without images
        self.default_icon = pygame.Surface((32, 32), pygame.SRCALPHA)
        pygame.draw.rect(self.default_icon, (100, 100, 100), (0, 0, 32, 32))
        pygame.draw.rect(self.default_icon, (200, 200, 200), (0, 0, 32, 32), 1)

        # Apply initial filtering
        self._apply_category_filter()



    def _apply_category_filter(self):
        """Filter recipes based on the selected category."""
        if self.selected_category == "All":
            self.filtered_recipes = self.recipes.copy()
            return

        # Filter recipes based on output item type
        self.filtered_recipes = []
        for recipe in self.recipes:
            output_item = recipe.get("output", "")
            item_data = self.items_data.get(output_item, {})
            item_type = item_data.get("type", "").lower()

            # Map item types to categories
            category_map = {
                "weapon": "Weapons",
                "tool": "Tools",
                "armor": "Armor",
                "potion": "Potions",
                "resource": "Resources",
                "food": "Food"
            }

            if category_map.get(item_type) == self.selected_category:
                self.filtered_recipes.append(recipe)

        # Reset scroll offset when changing categories
        self.scroll_offset = 0

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.KEYDOWN:
                if event.key in (pygame.K_ESCAPE, pygame.K_c):
                    self.gs.current_state = "gameplay"
                elif event.key == pygame.K_UP:
                    self.scroll_offset = max(0, self.scroll_offset - 1)
                elif event.key == pygame.K_DOWN:
                    self.scroll_offset = min(len(self.filtered_recipes) - 1, self.scroll_offset + 1)
                elif event.key == pygame.K_RETURN:
                    if self.filtered_recipes:
                        recipe = self.filtered_recipes[self.scroll_offset]
                        if self.try_craft(recipe):
                            self.gs.show_feedback_message(f"Crafted {recipe['name']}!", color=(0,255,0))
                        else:
                            self.gs.show_feedback_message("Missing ingredients!", color=(255,0,0))
            elif event.type == pygame.MOUSEMOTION:
                pos = event.pos
                self.hovered_index = None
                for idx, rect in enumerate(self.recipe_button_rects):
                    if rect.collidepoint(pos):
                        self.hovered_index = idx
                        break
            elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                pos = event.pos

                # Check category buttons
                for idx, (rect, category) in enumerate(self.category_buttons):
                    if rect.collidepoint(pos):
                        self.selected_category = category
                        self._apply_category_filter()
                        break

                # Select recipe
                for idx, rect in enumerate(self.recipe_button_rects):
                    if rect.collidepoint(pos):
                        self.scroll_offset = idx
                        break
                # Craft
                if self.craft_button_rect and self.craft_button_rect.collidepoint(pos):
                    if self.filtered_recipes:
                        recipe = self.filtered_recipes[self.scroll_offset]
                        if self.try_craft(recipe):
                            self.gs.show_feedback_message(f"Crafted {recipe['name']}!", color=(0,255,0))
                        else:
                            self.gs.show_feedback_message("Missing ingredients!", color=(255,0,0))
                # Back
                if self.back_button_rect and self.back_button_rect.collidepoint(pos):
                    self.gs.current_state = "gameplay"

    def try_craft(self, recipe):
        # Real crafting logic: check inventory and update items
        import main
        ecs_world = main.ecs_world
        from core.components import InventoryComponent

        player = ecs_world.get_player_entity()
        if not player:
            return False
        inv_comp = player.get_component(InventoryComponent)
        if not inv_comp:
            return False

        # Check required ingredients using get_item_count for case-insensitive lookup
        for name, count in recipe.get("ingredients", {}).items():
            if inv_comp.get_item_count(name) < count:
                return False

        # Deduct ingredients using remove_item for proper case-insensitive handling
        for name, count in recipe.get("ingredients", {}).items():
            inv_comp.remove_item(name, count)

        # Add crafted item(s) using add_item for proper case handling
        out = recipe.get("output")
        qty = recipe.get("output_qty", 1)
        inv_comp.add_item(out, qty)

        return True

    def update(self, dt):
        pass

    def render(self, screen):
        screen.fill((30,30,30))
        draw_text(screen, self.font, "Crafting", 40, 40, color=(255,255,0))

        # Get player inventory
        import main as _main; ecs_world = _main.ecs_world
        player = ecs_world.get_player_entity()
        inv_comp = None
        if player:
            inv_comp = player.get_component(InventoryComponent)

        # Draw category filter buttons
        self.category_buttons = []
        category_x = 40
        category_y = 80
        for category in self.categories:
            # Highlight selected category
            is_selected = category == self.selected_category
            button_color = (100, 100, 150) if is_selected else (70, 70, 100)
            hover_color = (120, 120, 170) if is_selected else (90, 90, 120)

            # Draw category button
            _, rect = draw_button(
                screen, self.small_font, category, category_x, category_y, 80, 30,
                button_color, hover_color
            )
            self.category_buttons.append((rect, category))
            category_x += 90

            # Draw selection indicator
            if is_selected:
                pygame.draw.rect(screen, (200, 200, 255), rect, 2)

        # Draw recipe list on left
        y = 130  # Start recipes below category buttons
        self.recipe_button_rects = []

        if not self.filtered_recipes:
            draw_text(screen, self.font, "No recipes in this category", 40, y + 20, color=(200, 200, 200))

        for i, recipe in enumerate(self.filtered_recipes):
            # Determine if recipe can be crafted
            can_craft = False
            if inv_comp:
                can_craft = True
                for name, count in recipe.get("ingredients", {}).items():
                    # Use case-insensitive lookup
                    if inv_comp.get_item_count(name) < count:
                        can_craft = False
                        break

            # Set button color based on craftability
            button_color = (80, 120, 80) if can_craft else (80, 80, 80)
            hover_color = (120, 160, 120) if can_craft else (120, 120, 120)

            _, rect = draw_button(
                screen, self.font, recipe.get("name", ""), 40, y, 200, 40,
                button_color, hover_color if i == self.hovered_index else button_color
            )
            if i == self.scroll_offset:
                pygame.draw.rect(screen, (0,200,0), rect, 2)
            self.recipe_button_rects.append(rect)
            y += 50

        # Draw details for selected recipe on right
        x0, y0 = 300, 100
        if self.filtered_recipes and self.scroll_offset < len(self.filtered_recipes):
            recipe = self.filtered_recipes[self.scroll_offset]

            # Draw recipe name and description
            recipe_name = recipe.get("name", "Unknown Recipe")
            draw_text(screen, self.font, recipe_name, x0, y0, color=(255,255,0))

            desc = recipe.get("description", "No description.")
            draw_text(screen, self.font, desc, x0, y0 + 30, color=(255,255,255))

            # Draw output item info
            output_item = recipe.get("output", "")
            output_qty = recipe.get("output_qty", 1)

            y_output = y0 + 70
            draw_text(screen, self.font, "Creates:", x0, y_output, color=(255,255,0))

            # Get item data and icon
            item_data = self.items_data.get(output_item, {})
            icon = self.item_icons.get(output_item.lower(), self.default_icon)

            # Draw output item with icon
            screen.blit(icon, (x0, y_output + 30))
            item_name = output_item.replace('_', ' ').title()
            draw_text(screen, self.font, f"{item_name} x{output_qty}",
                     x0 + icon.get_width() + 10, y_output + 30 + (icon.get_height()//2),
                     color=(255,255,255))

            # Draw item type and rarity if available
            item_type = item_data.get("type", "")
            item_rarity = item_data.get("rarity", "")
            if item_type or item_rarity:
                type_text = f"Type: {item_type.capitalize()}" if item_type else ""
                rarity_text = f"Rarity: {item_rarity.capitalize()}" if item_rarity else ""
                combined_text = f"{type_text}  {rarity_text}".strip()
                draw_text(screen, self.font, combined_text,
                         x0, y_output + 70, color=(200,200,200))

            # Draw ingredients section
            y1 = y_output + 110
            draw_text(screen, self.font, "Required Ingredients:", x0, y1, color=(255,255,0))

            for idx, (name, count) in enumerate(recipe.get("ingredients", {}).items()):
                # Get the actual count using get_item_count for case-insensitive lookup
                actual_count = 0
                if inv_comp:
                    # Ensure we're passing a string to get_item_count
                    item_name_str = str(name) if name is not None else ""
                    actual_count = inv_comp.get_item_count(item_name_str)

                # Get item data
                item_data = self.items_data_lower.get(str(name).lower(), {})

                # Use lowercase key for icon lookup
                icon = self.item_icons.get(str(name).lower(), self.default_icon)

                # Log missing icons for debugging
                if name is not None and icon is self.default_icon:
                    import logging
                    logging.debug(f"Missing icon for item: {name}")

                y_offset = y1 + (idx+1) * 40
                screen.blit(icon, (x0, y_offset))

                # Format item name and count
                item_name = name.replace('_', ' ').title()

                # Color based on whether player has enough
                text_color = (255,255,255) if actual_count >= count else (255,100,100)
                item_text = f"{item_name}: {actual_count}/{count}"

                # Draw text next to icon
                text_x = x0 + icon.get_width() + 10
                draw_text(screen, self.font, item_text, text_x, y_offset + (icon.get_height()//2), color=text_color)

        # Draw player inventory section
        if inv_comp:
            inv_x = 600
            inv_y = 100
            draw_text(screen, self.font, "Your Inventory:", inv_x, inv_y, color=(255,255,0))

            # Draw scrollable inventory grid
            items_per_row = 3
            item_size = 40
            padding = 10
            max_items_visible = 15  # Show 5 rows of 3 items

            # Convert inventory to list for easier display
            inventory_items = list(inv_comp.items.items())

            # Track hovered item for tooltip
            hovered_item_info = None
            mouse_pos = pygame.mouse.get_pos()

            for i in range(min(max_items_visible, len(inventory_items))):
                item_name, qty = inventory_items[i]

                row = i // items_per_row
                col = i % items_per_row

                x = inv_x + col * (item_size + padding)
                y = inv_y + 40 + row * (item_size + padding)

                # Create item rect for hover detection
                item_rect = pygame.Rect(x, y, item_size, item_size)

                # Check if mouse is hovering over this item
                if item_rect.collidepoint(mouse_pos):
                    # Get item data for tooltip
                    item_data = None
                    if item_name is not None:
                        item_data = self.items_data_lower.get(str(item_name).lower())
                        # Log missing item data for debugging
                        if not item_data:
                            import logging
                            logging.debug(f"Missing item data for: {item_name}")

                    if item_data:
                        hovered_item_info = {
                            "name": item_name.replace('_', ' ').title(),
                            "description": item_data.get("description", "No description available."),
                            "type": item_data.get("type", ""),
                            "rarity": item_data.get("rarity", ""),
                            "quantity": qty
                        }
                    else:
                        hovered_item_info = {
                            "name": item_name.replace('_', ' ').title(),
                            "description": "No description available.",
                            "quantity": qty
                        }

                # Draw item background with highlight if hovered
                bg_color = (80,80,80) if item_rect.collidepoint(mouse_pos) else (60,60,60)
                pygame.draw.rect(screen, bg_color, item_rect)
                pygame.draw.rect(screen, (100,100,100), item_rect, 1)  # Border

                # Draw item icon if available
                icon = self.item_icons.get(str(item_name).lower(), self.default_icon)

                # Log missing icons for debugging
                if item_name is not None and icon is self.default_icon:
                    import logging
                    logging.debug(f"Missing icon for inventory item: {item_name}")

                icon_scaled = pygame.transform.scale(icon, (item_size-4, item_size-4))
                screen.blit(icon_scaled, (x+2, y+2))

                # Draw quantity
                qty_text = f"x{qty}"
                draw_text(screen, pygame.font.Font(None, 20), qty_text,
                         x + item_size - 15, y + item_size - 15, color=(255,255,255))

            # Draw tooltip for hovered item
            if hovered_item_info:
                tooltip_width = 200
                tooltip_x = min(mouse_pos[0] + 20, screen.get_width() - tooltip_width - 10)
                tooltip_y = mouse_pos[1] + 20

                # Background
                tooltip_height = 80
                if "type" in hovered_item_info and "rarity" in hovered_item_info:
                    tooltip_height += 20

                tooltip_rect = pygame.Rect(tooltip_x, tooltip_y, tooltip_width, tooltip_height)
                pygame.draw.rect(screen, (40, 40, 40), tooltip_rect)
                pygame.draw.rect(screen, (100, 100, 100), tooltip_rect, 1)

                # Item name
                draw_text(screen, self.font, hovered_item_info["name"],
                         tooltip_x + 10, tooltip_y + 10, color=(255, 255, 0))

                # Quantity
                draw_text(screen, self.font, f"Quantity: {hovered_item_info['quantity']}",
                         tooltip_x + 10, tooltip_y + 30, color=(200, 200, 200))

                # Type and rarity if available
                if "type" in hovered_item_info and "rarity" in hovered_item_info:
                    type_text = f"Type: {hovered_item_info['type'].capitalize()}" if hovered_item_info["type"] else ""
                    rarity_text = f"Rarity: {hovered_item_info['rarity'].capitalize()}" if hovered_item_info["rarity"] else ""
                    combined_text = f"{type_text}  {rarity_text}".strip()
                    draw_text(screen, self.font, combined_text,
                             tooltip_x + 10, tooltip_y + 50, color=(200, 200, 200))

                    # Description
                    description = hovered_item_info["description"]
                    # Truncate if too long
                    if len(description) > 30:
                        description = description[:27] + "..."
                    draw_text(screen, self.font, description,
                             tooltip_x + 10, tooltip_y + 70, color=(255, 255, 255))
                else:
                    # Description
                    description = hovered_item_info["description"]
                    # Truncate if too long
                    if len(description) > 30:
                        description = description[:27] + "..."
                    draw_text(screen, self.font, description,
                             tooltip_x + 10, tooltip_y + 50, color=(255, 255, 255))

        # Draw Craft and Back buttons
        # Determine if current recipe can be crafted
        can_craft = False
        if self.filtered_recipes and self.scroll_offset < len(self.filtered_recipes) and inv_comp:
            recipe = self.filtered_recipes[self.scroll_offset]
            can_craft = True
            for name, count in recipe.get("ingredients", {}).items():
                if inv_comp.get_item_count(name) < count:
                    can_craft = False
                    break

        craft_color = (70,150,70) if can_craft else (70,70,70)
        craft_hover = (100,180,100) if can_craft else (100,100,100)

        # Draw recipe count
        recipe_count_text = f"Showing {len(self.filtered_recipes)} of {len(self.recipes)} recipes"
        draw_text(screen, self.small_font, recipe_count_text, 800, 70, color=(200, 200, 200))

        _, self.craft_button_rect = draw_button(screen, self.font, "Craft", 800, 100, 120, 40,
                                               craft_color, craft_hover)
        _, self.back_button_rect = draw_button(screen, self.font, "Back", 800, 160, 120, 40,
                                              (70,70,70), (100,100,100))

        # Only flip the display if we're not in a test environment
        try:
            pygame.display.flip()
        except pygame.error:
            # Skip display flip during testing
            pass
