"""Loading screen implementation for the game."""

# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union, Type, TypeVar

# Third-party imports
import pygame

# Local application imports
from core.components import PositionComponent
from src.ui_util import draw_text

# Dependencies injected via constructor
class LoadingScreen:
    def __init__(self, gs, wmgr, eworld, safe_quit, font, window_width, window_height):
        self.gs = gs
        self.wmgr = wmgr
        self.eworld = eworld
        self.safe_quit = safe_quit
        self.font = font
        self.window_width = window_width
        self.window_height = window_height
        # Prepare list of region coordinates to load
        grid = self.wmgr.terrain_generator.region_grid_size
        center = grid // 2
        self.coords = [(center + dx, center + dy)
                       for dx in range(-center, center + 1)
                       for dy in range(-center, center + 1)]
        self.total = len(self.coords)
        self.loaded = 0

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.safe_quit()

    def update(self, dt):
        # Load world regions progressively
        if self.loaded < self.total:
            rx, ry = self.coords[self.loaded]
            self.wmgr.load_region(rx, ry)
            self.loaded += 1
        else:
            # Aggregate full world map
            region_size = self.wmgr.terrain_generator.region_size
            grid = self.wmgr.terrain_generator.region_grid_size
            world_w = grid * region_size
            world_h = grid * region_size
            full_tiles = [[None for _ in range(world_w)] for _ in range(world_h)]
            for (rx, ry), region in self.wmgr.active_regions.items():
                for y, row in enumerate(region.get("tiles", [])):
                    for x, tile in enumerate(row):
                        gx = rx * region_size + x
                        gy = ry * region_size + y
                        full_tiles[gy][gx] = tile
            self.wmgr.world_map["tiles"] = full_tiles
            # Place player at settlement
            pos = self.wmgr.find_settlement_in_biome(self.gs.player_biome_name) or {"x": world_w // 2, "y": world_h // 2}
            player = self.eworld.get_player_entity()
            if player:
                pc = player.get_component(PositionComponent)
                if pc:
                    pc.x, pc.y = pos["x"], pos["y"]
                else:
                    player.add_component(PositionComponent(pos["x"], pos["y"]))
            # Transition to gameplay
            self.gs.current_state = "gameplay"

    def render(self, screen):
        # Draw loading bar and percentage
        screen.fill((0, 0, 0))
        margin = 50
        bar_w = self.window_width - margin * 2
        bar_h = 30
        x = margin
        y = self.window_height // 2 - bar_h // 2
        pygame.draw.rect(screen, (80, 80, 80), (x, y, bar_w, bar_h))
        prog = self.loaded / self.total if self.total else 1
        pygame.draw.rect(screen, (0, 200, 0), (x, y, bar_w * prog, bar_h))
        draw_text(screen, self.font, f"Loading: {int(prog * 100)}%", self.window_width // 2, y - 40)
        pygame.display.flip()
