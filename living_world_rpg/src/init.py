# File: src/init.py
"""
Initialization Module for Living World RPG

This module sets up the core game systems by:
  - Loading all configuration data via the DataLoader.
  - Creating an ECSWorld instance.
  - Initializing the WorldManager with loaded data.

Returns:
  - ecs_world: The core ECS world instance.
  - data: Dictionary containing all game configuration data.
  - world_manager: The world manager that handles terrain, settlements, events, etc.
"""

import logging
from core.ecs import ECSWorld
from data_loader import DataLoader
from world.world_manager import WorldManager

def initialize_game():
    logging.basicConfig(level=logging.INFO)
    loader = DataLoader()
    data = loader.load_all_data()
    ecs_world = ECSWorld()
    world_manager = WorldManager(ecs_world, data)
    return ecs_world, data, world_manager