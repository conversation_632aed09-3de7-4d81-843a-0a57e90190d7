"""
Faction Manager for Living World RPG

- Provides access to faction data and relationships.
- Returns allied and rival factions as well as faction-specific preferences.
"""

# Standard library imports
import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
# (none in this file)

# Configure logging
logging.basicConfig(level=logging.INFO)

class FactionManager:
    def __init__(self, faction_data: dict):
        self.factions = faction_data

    def get_faction_info(self, faction_name: str) -> dict:
        return self.factions.get(faction_name, {})

    def get_allies(self, faction_name: str) -> list:
        faction_info = self.get_faction_info(faction_name)
        return faction_info.get("allied_factions", [])

    def get_rivals(self, faction_name: str) -> list:
        faction_info = self.get_faction_info(faction_name)
        return faction_info.get("rival_factions", [])

    def get_faction_goals(self, faction_name: str) -> list:
        faction_info = self.get_faction_info(faction_name)
        return faction_info.get("goals", [])

    def preferred_biome(self, faction_name: str) -> str:
        faction_info = self.get_faction_info(faction_name)
        return faction_info.get("preferred_biome", "Unknown")
