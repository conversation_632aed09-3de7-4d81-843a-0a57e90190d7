"""
World History Generator for Living World RPG

- Procedurally generates a history log over a number of in-game years.
- Produces events such as wars, alliances, discoveries, disasters, and migrations.
"""

# Standard library imports
import datetime
import random
from typing import Any, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
# (none in this file)

class WorldHistory:
    def __init__(self, factions: dict, settlements: dict):
        self.factions = factions
        self.settlements = settlements
        self.history = []

    def generate_history(self, years: int = 100) -> list:
        self.history = []
        current_year = datetime.datetime.now().year - years
        for _ in range(years):
            current_year += 1
            event = self.generate_event(current_year)
            if event:
                self.history.append(event)
        return self.history

    def generate_event(self, year: int) -> dict:
        event_type = random.choice(["war", "alliance", "discovery", "disaster", "founding", "migration", "economic_boom"])
        if event_type == "war":
            return self.generate_war_event(year)
        elif event_type == "alliance":
            return self.generate_alliance_event(year)
        elif event_type == "discovery":
            return self.generate_discovery_event(year)
        elif event_type == "disaster":
            return self.generate_disaster_event(year)
        elif event_type == "founding":
            return self.generate_founding_event(year)
        elif event_type == "migration":
            return self.generate_migration_event(year)
        elif event_type == "economic_boom":
            return self.generate_economic_boom_event(year)
        return {}

    def generate_war_event(self, year: int) -> dict:
        factions = random.sample(list(self.factions.keys()), 2)
        winner = random.choice(factions)
        loser = factions[0] if factions[1] == winner else factions[1]
        self.factions[winner]["power"] = self.factions[winner].get("power", 1) + 1
        self.factions[loser]["power"] = max(0, self.factions[loser].get("power", 1) - 1)
        return {
            "year": year,
            "type": "war",
            "details": f"A war between {factions[0]} and {factions[1]}; {winner} emerged victorious.",
            "winner": winner,
            "loser": loser
        }

    def generate_alliance_event(self, year: int) -> dict:
        factions = random.sample(list(self.factions.keys()), 2)
        return {
            "year": year,
            "type": "alliance",
            "details": f"{factions[0]} and {factions[1]} formed an alliance.",
            "factions_involved": factions
        }

    def generate_discovery_event(self, year: int) -> dict:
        settlement = random.choice(list(self.settlements.keys()))
        resource = random.choice(["gold", "magic crystal", "ancient artifact", "rare herbs"])
        return {
            "year": year,
            "type": "discovery",
            "details": f"Explorers in {settlement} discovered a deposit of {resource}.",
            "settlement": settlement,
            "resource": resource
        }

    def generate_disaster_event(self, year: int) -> dict:
        settlement = random.choice(list(self.settlements.keys()))
        biome = self.settlements[settlement].get("biome", "Unknown")
        disaster = random.choice(["volcanic eruption", "flood", "plague", "earthquake"])
        return {
            "year": year,
            "type": "disaster",
            "details": f"{settlement} suffered a {disaster} in the {biome}.",
            "settlement": settlement,
            "biome": biome,
            "disaster_type": disaster
        }

    def generate_founding_event(self, year: int) -> dict:
        settlement_name = f"New Settlement {random.randint(1, 1000)}"
        while settlement_name in self.settlements:
            settlement_name = f"New Settlement {random.randint(1, 1000)}"
        faction = random.choice(list(self.factions.keys()))
        self.settlements[settlement_name] = {
            "faction": faction,
            "biome": "Unknown",
            "production_rate": {},
            "consumption_rate": {}
        }
        return {
            "year": year,
            "type": "founding",
            "details": f"{settlement_name} was founded by {faction}.",
            "settlement": settlement_name,
            "faction": faction
        }

    def generate_migration_event(self, year: int) -> dict:
        settlement_from = random.choice(list(self.settlements.keys()))
        settlement_to = random.choice(list(self.settlements.keys()))
        while settlement_to == settlement_from:
            settlement_to = random.choice(list(self.settlements.keys()))
        moved = random.randint(10, 100)
        return {
            "year": year,
            "type": "migration",
            "details": f"{moved} people migrated from {settlement_from} to {settlement_to}.",
            "from": settlement_from,
            "to": settlement_to,
            "population": moved
        }

    def generate_economic_boom_event(self, year: int) -> dict:
        settlement = random.choice(list(self.settlements.keys()))
        resource = random.choice(["gold", "spices", "iron", "magic crystals"])
        return {
            "year": year,
            "type": "economic_boom",
            "details": f"{settlement} experienced an economic boom thanks to {resource}.",
            "settlement": settlement,
            "resource": resource
        }

    def display_history(self) -> None:
        for event in self.history:
            print(f"{event['year']}: {event['details']}")
