"""
Biome Manager for Living World RPG

- Validates biome thresholds for temperature, rainfall, and height.
- Provides lookup methods for biome data.
- Offers a method to generate additional terrain details for a given biome.
"""

# Standard library imports
import logging
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
# (none in this file)

# Configure logging
logging.basicConfig(level=logging.WARNING)

class BiomeManager:
    def __init__(self, biome_data: dict):
        self.biome_data = biome_data
        self.thresholds = defaultdict(dict)
        self.validate_biome_thresholds()
        logging.info(f"[BiomeManager] Initialized with {len(biome_data)} biomes")

        # Log the available biomes for debugging
        for biome_name in biome_data.keys():
            logging.info(f"[BiomeManager] Available biome: {biome_name}")

    def validate_biome_thresholds(self):
        """
        Validates biome thresholds and logs the ranges for each biome.
        Raises ValueError if biome data is missing or invalid.
        """
        if not self.biome_data:
            raise ValueError("[BiomeManager] No biome data provided")

        temp_ranges = []
        rain_ranges = []
        height_ranges = []

        # Validate each biome's data structure
        for biome_name, b_data in self.biome_data.items():
            if not isinstance(b_data, dict):
                raise ValueError(f"[BiomeManager] Invalid biome data for {biome_name}")

            thresholds = b_data.get("thresholds")
            if not thresholds or not isinstance(thresholds, dict):
                raise ValueError(f"[BiomeManager] Missing or invalid thresholds for {biome_name}")

            t_range = thresholds.get("temperature", {})
            r_range = thresholds.get("rainfall", {})
            h_range = thresholds.get("height", {})

            try:
                tmin = float(t_range.get("min", float('-inf')))
                tmax = float(t_range.get("max", float('inf')))
                rmin = float(r_range.get("min", float('-inf')))
                rmax = float(r_range.get("max", float('inf')))
                hmin = float(h_range.get("min", 0))
                hmax = float(h_range.get("max", 1))
            except (ValueError, TypeError) as e:
                raise ValueError(f"[BiomeManager] Invalid threshold values for {biome_name}: {str(e)}")

            # Validate range logic
            if tmax < tmin or rmax < rmin or hmax < hmin:
                raise ValueError(f"[BiomeManager] Invalid range values for {biome_name}: max cannot be less than min")

            # Store validated ranges
            self.thresholds[biome_name] = {
                "temperature": (tmin, tmax),
                "rainfall": (rmin, rmax),
                "height": (hmin, hmax)
            }

            # Store ranges for logging
            temp_ranges.append((tmin, tmax, biome_name))
            rain_ranges.append((rmin, rmax, biome_name))
            height_ranges.append((hmin, hmax, biome_name))

        # Sort and log the ranges for easier debugging
        temp_ranges.sort(key=lambda x: (x[0], x[1]))
        rain_ranges.sort(key=lambda x: (x[0], x[1]))
        height_ranges.sort(key=lambda x: (x[0], x[1]))

        # Log the ranges
        logging.info("[BiomeManager] Temperature ranges:")
        for tmin, tmax, name in temp_ranges:
            logging.info(f"  {name}: {tmin} to {tmax}")

        logging.info("[BiomeManager] Rainfall ranges:")
        for rmin, rmax, name in rain_ranges:
            logging.info(f"  {name}: {rmin} to {rmax}")

        logging.info("[BiomeManager] Height ranges:")
        for hmin, hmax, name in height_ranges:
            logging.info(f"  {name}: {hmin} to {hmax}")

        logging.info("[BiomeManager] Validated ranges for all biomes successfully")

    def get_biome(self, biome_name: str) -> dict:
        return self.biome_data.get(biome_name, {})

    def create_biome_terrains(self, terrain_generator) -> None:
        """
        For each biome, generate extra terrain features.

        Args:
            terrain_generator: The terrain generator instance to use

        Note:
            This method is currently a placeholder for future implementation.
        """
        # Placeholder for future implementation
        # Suppress unused parameter warning
        _ = terrain_generator

        # Future implementation will look like:
        # for biome_name in self.biome_data:
        #     terrain_data = terrain_generator.generate_terrain_for_biome(biome_name)
        #     logging.debug(f"[BiomeManager] Generated terrain data for '{biome_name}': {terrain_data}")
        pass

    def get_biome_by_conditions(self, temperature: float, rainfall: float, height: float) -> str:
        """
        Get the most appropriate biome for the given environmental conditions.

        Args:
            temperature: Temperature value in degrees
            rainfall: Rainfall value in mm
            height: Height value from 0.0 to 1.0

        Returns:
            The name of the most appropriate biome
        """
        # First attempt: exact match within thresholds
        for biome_name, ranges in self.thresholds.items():
            tmin, tmax = ranges["temperature"]
            rmin, rmax = ranges["rainfall"]
            hmin, hmax = ranges["height"]
            if tmin <= temperature <= tmax and rmin <= rainfall <= rmax and hmin <= height <= hmax:
                return biome_name

        # Second attempt: relaxed matching with expanded ranges (10% expansion)
        for biome_name, ranges in self.thresholds.items():
            tmin, tmax = ranges["temperature"]
            rmin, rmax = ranges["rainfall"]
            hmin, hmax = ranges["height"]

            # Expand ranges by 10%
            t_range = tmax - tmin
            r_range = rmax - rmin
            h_range = hmax - hmin

            expanded_tmin = tmin - (t_range * 0.1)
            expanded_tmax = tmax + (t_range * 0.1)
            expanded_rmin = rmin - (r_range * 0.1)
            expanded_rmax = rmax + (r_range * 0.1)
            expanded_hmin = max(0.0, hmin - (h_range * 0.1))
            expanded_hmax = min(1.0, hmax + (h_range * 0.1))

            if (expanded_tmin <= temperature <= expanded_tmax and
                expanded_rmin <= rainfall <= expanded_rmax and
                expanded_hmin <= height <= expanded_hmax):
                return biome_name

        # Final fallback: assign to nearest biome centroid
        import math
        best_biome = None
        best_dist = float('inf')

        # Normalize the values to give equal weight
        max_temp_range = 90  # -40 to 50
        max_rain_range = 300  # 0 to 300
        max_height_range = 1.0  # 0.0 to 1.0

        norm_temp = temperature / max_temp_range
        norm_rain = rainfall / max_rain_range
        norm_height = height / max_height_range

        for biome_name, ranges in self.thresholds.items():
            tmin, tmax = ranges["temperature"]
            rmin, rmax = ranges["rainfall"]
            hmin, hmax = ranges["height"]

            # Calculate normalized center point
            center_temp = ((tmin + tmax) / 2) / max_temp_range
            center_rain = ((rmin + rmax) / 2) / max_rain_range
            center_height = ((hmin + hmax) / 2) / max_height_range

            # Calculate weighted distance (height has more weight to preserve terrain features)
            dist = math.sqrt(
                (norm_temp - center_temp)**2 +
                (norm_rain - center_rain)**2 +
                (norm_height - center_height)**2 * 1.5  # Height has 50% more weight
            )

            if dist < best_dist:
                best_dist = dist
                best_biome = biome_name

        # Only log a warning if we're using the fallback method
        logging.debug(f"[BiomeManager] No matching biome for T={temperature}, R={rainfall}, H={height}; using '{best_biome}'")
        return best_biome

    def is_valid_biome(self, biome_name: str) -> bool:
        """
        Quickly checks if the biome name exists within biome data.
        """
        return biome_name in self.biome_data
