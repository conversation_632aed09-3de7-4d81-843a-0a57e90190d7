"""
Resource Manager for Living World RPG

- Initializes resource maps for each biome using biome definitions.
- Provides methods to add, remove, and display resources.
"""

# Standard library imports
import logging
import random
from typing import Any, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
from utils.elastic_dict_adapter import ElasticDictAdapter

# Configure logging
logging.basicConfig(level=logging.INFO)

class ResourceManager:
    def __init__(self, biomes_data: dict, settlements_data: dict, factions_data: dict):
        self.biomes_data = biomes_data
        self.settlements_data = settlements_data
        self.factions_data = factions_data
        self.resource_map = ElasticDictAdapter(size=64)  # Maps biomes to resource layers
        self.initialize_resources()

    def initialize_resources(self) -> None:
        for biome_name, biome_info in self.biomes_data.items():
            layers = ElasticDictAdapter(size=8)  # Few layers per biome
            layers["surface"] = ElasticDictAdapter(size=32)  # Resources per layer
            layers["subsurface"] = ElasticDictAdapter(size=32)
            layers["deep"] = ElasticDictAdapter(size=32)
            self.resource_map[biome_name] = layers
            richness = biome_info.get("richness", 1.0)
            resources_list = biome_info.get("resources", [])
            for res_def in resources_list:
                if not isinstance(res_def, dict):
                    logging.warning(f"[ResourceManager] Invalid resource entry in {biome_name}: {res_def}")
                    continue
                rname = res_def.get("name")
                rarity = res_def.get("rarity", "Common")
                layer = res_def.get("type", "surface")
                if not rname:
                    logging.warning(f"[ResourceManager] Missing resource name in {biome_name}.")
                    continue
                base_qty = self.get_quantity_by_rarity(rarity, richness)
                self.resource_map[biome_name][layer][rname] = {"quantity": base_qty, "rarity": rarity}
        logging.info("[ResourceManager] Resources initialized for all biomes.")

    def get_quantity_by_rarity(self, rarity: str, richness: float) -> int:
        base = {"Common": 100, "Uncommon": 50, "Rare": 20, "Legendary": 5}.get(rarity, 100)
        return int(base * richness)

    def add_resource(self, biome: str, resource: str, qty: float, layer: str = "surface") -> None:
        if qty < 0:
            logging.error(f"[ResourceManager] Negative addition: {qty} of {resource} in {biome}.")
            return
        if biome not in self.resource_map or layer not in self.resource_map[biome]:
            logging.warning(f"[ResourceManager] Invalid biome or layer: {biome}, {layer}.")
            return
        if resource not in self.resource_map[biome][layer]:
            self.resource_map[biome][layer][resource] = {"quantity": 0, "rarity": "Common"}
        self.resource_map[biome][layer][resource]["quantity"] += qty

    def remove_resource(self, biome: str, resource: str, qty: float, layer: str = "surface") -> None:
        if qty < 0:
            logging.error(f"[ResourceManager] Negative removal: {qty} of {resource} from {biome}.")
            return
        if biome not in self.resource_map or layer not in self.resource_map[biome]:
            logging.warning(f"[ResourceManager] Invalid biome or layer: {biome}, {layer}.")
            return
        if resource in self.resource_map[biome][layer]:
            current = self.resource_map[biome][layer][resource]["quantity"]
            self.resource_map[biome][layer][resource]["quantity"] = max(0, current - qty)

    def get_default_resources(self) -> dict:
        return {"Wood": {"quantity": 50, "rarity": "Common"},
                "Stone": {"quantity": 20, "rarity": "Common"}}

    def display_resources(self) -> None:
        logging.info("[ResourceManager] Current Resource Map:")
        for biome, layers in self.resource_map.items():
            logging.info(f"Biome: {biome}")
            for layer, res_dict in layers.items():
                for rname, data in res_dict.items():
                    logging.info(f"  {layer} - {rname}: {data['quantity']} ({data['rarity']})")
