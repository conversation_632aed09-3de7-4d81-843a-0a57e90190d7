"""
Settlement Manager for Living World RPG

- Initializes settlement entities using data from settlements.json.
- Creates and assigns components for settlements, including resource inventories.
"""

# Standard library imports
import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
from core.components import FactionComponent, InventoryComponent, SettlementComponent

# Configure logging
logging.basicConfig(level=logging.INFO)

class SettlementManager:
    def __init__(self, ecs_world, faction_manager, biome_manager, settlements_data: dict):
        self.ecs_world = ecs_world
        self.faction_manager = faction_manager
        self.biome_manager = biome_manager
        self.settlements_data = settlements_data
        required_keys = ("biome", "faction", "production_rate", "consumption_rate")
        for name, data in settlements_data.items():
            missing = [k for k in required_keys if k not in data]
            if missing:
                raise ValueError(f"Settlement '{name}' is missing keys: {missing}")

    def init_settlements(self) -> None:
        for name, data in self.settlements_data.items():
            biome_name = data["biome"]
            faction_name = data["faction"]
            prod_rate = data["production_rate"]
            cons_rate = data["consumption_rate"]
            # Calculate initial resources using a multiplier (placeholder)
            multiplier = 1.5
            init_resources = {res: int(qty * multiplier) for res, qty in prod_rate.items()}
            entity = self.ecs_world.create_entity()
            settlement = SettlementComponent(name, biome_name, faction_name, prod_rate, cons_rate)
            # For now, we place settlements at fixed coordinates (to be improved later)
            settlement.x = 50
            settlement.y = 50
            inventory = InventoryComponent(init_resources)
            faction = FactionComponent(faction_name)
            entity.add_component(settlement)
            entity.add_component(inventory)
            entity.add_component(faction)
            logging.info(f"[SettlementManager] Created settlement '{name}' in biome '{biome_name}'.")

    def create_settlement(self, name: str, faction: str, biome_name: str,
                          production_rate: dict, consumption_rate: dict):
        entity = self.ecs_world.create_entity()
        settlement = SettlementComponent(name, biome_name, faction, production_rate, consumption_rate)
        inventory = InventoryComponent()
        faction_comp = FactionComponent(faction)
        entity.add_component(settlement)
        entity.add_component(inventory)
        entity.add_component(faction_comp)
        logging.info(f"[SettlementManager] Created settlement '{name}' controlled by '{faction}'.")
        return entity
