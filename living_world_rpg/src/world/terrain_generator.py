"""
Terrain Generator for Living World RPG

This module implements a refined terrain generation system with:
  - A continent mask for smooth landmass formation.
  - Structured temperature and humidity maps for logical biome placement.
  - Biome clustering and smoothing to create realistic ecological transitions.
"""

# Standard library imports
import logging
import os
import random
import time
from collections import Counter
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

# Third-party imports
import numpy as np
from scipy.ndimage import gaussian_filter, map_coordinates

# Local application imports
from utils.elastic_dict_adapter import ElasticDictAdapter
from world.biome_manager import BiomeManager

# Configure logging
logging.basicConfig(level=logging.INFO)


def generate_continent_mask(width, height, seed, blur_radius=15, land_threshold_percentile=42):
    """
    Generates a realistic continent mask with smooth  landmass distribution.

    Uses base random noise, applies a strong Gaussian blur, and thresholds the result to define land.
    """
    np.random.seed(seed)
    raw_mask = np.random.rand(height, width)
    smoothed_mask = gaussian_filter(raw_mask, sigma=blur_radius)
    land_threshold = np.percentile(smoothed_mask, land_threshold_percentile)
    return smoothed_mask > land_threshold


def generate_temperature_humidity_map(width, height, seed):
    """
    Creates structured temperature and humidity distributions for biome placement.

    Randomly selects a gradient direction for each map, ensuring each seed generates unique yet
    logically organized environmental gradients.
    """
    random.seed(seed)
    temp_direction = random.choice(["left-right", "top-bottom", "diagonal"])
    humidity_direction = random.choice(["left-right", "bottom-top", "diagonal"])

    temperature_map = np.zeros((height, width))
    humidity_map = np.zeros((height, width))

    for y in range(height):
        for x in range(width):
            if temp_direction == "left-right":
                temperature_map[y, x] = x / width
            elif temp_direction == "top-bottom":
                temperature_map[y, x] = y / height
            else:
                temperature_map[y, x] = (x + y) / (width + height)

            if humidity_direction == "left-right":
                humidity_map[y, x] = x / width
            elif humidity_direction == "bottom-top":
                humidity_map[y, x] = y / height
            else:
                humidity_map[y, x] = (x + y) / (width + height)

    return temperature_map, humidity_map


def assign_biome(temperature, rainfall, height):
    """
    Assigns biomes based on environmental conditions.
    Temperature is in range 0-100
    Rainfall is in range 0-200
    Height is in range 0-1
    """
    if height < 0.3:  # Low elevation
        if temperature > 80:
            return "Molten Fields" if rainfall < 50 else "Twilight Marsh"
        elif temperature > 60:
            return "Golden Sands Desert" if rainfall < 60 else "Verdant Forest"
        else:
            return "Frost-Hewn Tundra"
    else:  # Higher elevation
        if temperature > 70:
            return "Molten Fields"
        elif temperature > 50:
            return "Verdant Forest"
        else:
            return "Frost-Hewn Tundra"


def generate_clustered_biome_map(width, height, seed):
    """
    Generates a biome map with large, natural clusters.
    Uses Perlin noise to create smooth transitions between biomes.
    """
    np.random.seed(seed)

    # Generate base noise maps
    scale = 50.0  # Larger scale = bigger biome clusters
    temp_noise = generate_perlin_noise(width, height, scale, seed)
    rain_noise = generate_perlin_noise(width, height, scale, seed + 1)
    height_noise = generate_perlin_noise(width, height, scale, seed + 2)

    # Normalize the noise maps to 0-1 range
    temp_noise = (temp_noise + 1) / 2
    rain_noise = (rain_noise + 1) / 2
    height_noise = (height_noise + 1) / 2

    biome_map = np.empty((height, width), dtype=object)

    # Create continent mask for ocean placement
    continent_mask = gaussian_filter(np.random.rand(height, width), sigma=5)
    continent_mask = continent_mask > 0.45  # Adjust threshold to control land/ocean ratio

    for y in range(height):
        for x in range(width):
            if continent_mask[y, x]:
                # Scale the normalized values to their proper ranges
                temp = temp_noise[y, x] * 100  # 0-100 range
                rain = rain_noise[y, x] * 200  # 0-200 range
                height = height_noise[y, x]     # 0-1 range

                # Use these values to determine the biome
                biome_map[y, x] = assign_biome(temp, rain, height)
            else:
                biome_map[y, x] = "Ocean"

    return biome_map


def generate_perlin_noise(width: int, height: int, scale: float, seed: int) -> np.ndarray:
    """Generates smooth Perlin noise for natural-looking transitions."""
    np.random.seed(seed)

    # Generate a coarse noise grid
    coarse_size = (int(width/scale), int(height/scale))
    coarse = np.random.rand(*coarse_size)

    # Interpolate to desired size
    x = np.linspace(0, coarse_size[0]-1, width)
    y = np.linspace(0, coarse_size[1]-1, height)
    xv, yv = np.meshgrid(x, y)
    return map_coordinates(coarse, [yv, xv], order=1)


def gradient(x, y, grad):
    return x * grad[..., 0] + y * grad[..., 1]


def lerp(a0, a1, w):
    return (1.0 - w) * a0 + w * a1


def smooth(t):
    return t * t * (3.0 - 2.0 * t)


class TerrainGenerator:
    """
    Terrain Generator

    Implements a region-based world generation system with:
    - 5x5 grid of regions
    - Perlin noise-based environmental parameters
    - Seed-based climate variation
    - Biome classification using environmental thresholds
    """
    def __init__(self, biome_data: dict, color_palette: dict, map_width: int = 300,
                 map_height: int = 300, chunk_size: int = 150, seed: Optional[int] = None,
                 edge_fade_strength: Optional[float] = None, sea_level: Optional[float] = None):
        self.biome_data = biome_data
        self.color_palette = color_palette["biomes"] if "biomes" in color_palette else {}
        self.fallback_color = color_palette.get("fallbacks", {}).get("unknown_biome", "#808080")


        # Map dimensions
        self.map_width = map_width
        self.map_height = map_height
        
        # Region configuration
        self.region_grid_size = 5  # 5x5 grid of regions
        self.region_size = chunk_size  # Size of each region in tiles
        self.world_width = self.region_grid_size * self.region_size
        self.world_height = self.region_grid_size * self.region_size
        self.chunk_size = chunk_size
        self.edge_fade_strength = edge_fade_strength
        # Lower sea level to expose more land
        self.sea_level = sea_level if sea_level is not None else 0.3
        self.seed = seed if seed is not None else random.randint(0, 4294967295)

        # Initialize maps
        self.heightmap = None
        self.temperature_map = None
        self.rainfall_map = None
        self.biome_map = None
        self.starting_biome = None

        # Initialize biome manager
        self.biome_manager = BiomeManager(self.biome_data)

        logging.info(f"[TerrainGenerator] Initialized with seed={self.seed}, region_size={self.region_size}")
        self.validate_biome_colors()

    def generate_heightmap(self):
        """Generate elevation using Perlin noise with diagonal gradient"""
        logging.info("[TerrainGenerator] Generating heightmap...")
        np.random.seed(self.seed)
        noise = generate_perlin_noise(self.world_width, self.world_height, 50.0, self.seed)

        # Create diagonal gradient
        y, x = np.ogrid[0:self.world_height, 0:self.world_width]
        gradient = np.zeros((self.world_height, self.world_width))

        # High elevation at top-left and bottom-right for interesting terrain
        gradient += 1.0 - np.sqrt((x/self.world_width)**2 + (y/self.world_height)**2)
        gradient += 1.0 - np.sqrt(((self.world_width-x)/self.world_width)**2 +
                                ((self.world_height-y)/self.world_height)**2)

        # Normalize and combine with noise
        gradient = (gradient - gradient.min()) / (gradient.max() - gradient.min())
        self.heightmap = 0.7 * gradient + 0.3 * noise

        # Apply edge fade if specified
        if self.edge_fade_strength:
            edge_fade = np.ones((self.world_height, self.world_width))
            edge_fade *= (1 - self.edge_fade_strength)
            edge_fade += self.edge_fade_strength * gradient
            self.heightmap *= edge_fade

        # Final normalization
        self.heightmap = (self.heightmap - self.heightmap.min()) / (self.heightmap.max() - self.heightmap.min())
        logging.info(f"[TerrainGenerator] Heightmap generated with shape: {self.heightmap.shape}")
        logging.debug(f"[TerrainGenerator] Heightmap sample: {self.heightmap[:5, :5]}")

    def update_view_dimensions(self, width: int, height: int):
        """Update the view dimensions for the terrain generator."""
        self.view_width = width
        self.view_height = height

    def generate_temperature_map(self, rotation_deg: float = None):
        """Generate temperature with optional rotation"""
        noise = generate_perlin_noise(self.world_width, self.world_height, 60.0, self.seed + 1)

        # Base temperature gradient (north-south)
        y = np.linspace(0, 1, self.world_height)
        self.temperature_map = np.tile(y[:, np.newaxis], (1, self.world_width))

        # Apply rotation if specified
        if rotation_deg is not None:
            self.temperature_map = self.apply_climate_variation(
                self.temperature_map,
                rotation_deg,
                (random.uniform(-0.2, 0.2), random.uniform(-0.2, 0.2))
            )
        # Default rotation if none specified
        elif hasattr(self, 'temperature_map') and self.temperature_map is not None:
            # Use a default rotation of 0 degrees (no rotation)
            self.temperature_map = self.apply_climate_variation(
                self.temperature_map,
                0.0,
                (0.0, 0.0)
            )

        # Add noise and scale to temperature range (-40 to 50°C)
        self.temperature_map = (self.temperature_map + 0.2 * noise) * 90 - 40

    def generate_rainfall_map(self, rotation_deg: float = None):
        """Generate rainfall with optional rotation"""
        noise = generate_perlin_noise(self.world_width, self.world_height, 40.0, self.seed + 2)

        # Base rainfall gradient (east-west)
        x = np.linspace(0, 1, self.world_width)
        self.rainfall_map = np.tile(x[np.newaxis, :], (self.world_height, 1))

        # Apply rotation if specified
        if rotation_deg is not None:
            self.rainfall_map = self.apply_climate_variation(
                self.rainfall_map,
                rotation_deg,
                (random.uniform(-0.2, 0.2), random.uniform(-0.2, 0.2))
            )
        # Default rotation if none specified
        elif hasattr(self, 'rainfall_map') and self.rainfall_map is not None:
            # Use a default rotation of 0 degrees (no rotation)
            self.rainfall_map = self.apply_climate_variation(
                self.rainfall_map,
                0.0,
                (0.0, 0.0)
            )

        # Add noise and scale to rainfall range (0 to 300mm)
        self.rainfall_map = (self.rainfall_map + 0.2 * noise) * 300

    def apply_climate_variation(self, values: np.ndarray, rotation_deg: float,
                              translation: Tuple[float, float]) -> np.ndarray:
        """Apply seed-based variation to climate values."""
        height, width = values.shape

        # Create coordinate grids
        y, x = np.mgrid[0:height, 0:width]

        # Center coordinates
        x = x - width/2
        y = y - height/2

        # Apply rotation
        angle = np.radians(rotation_deg)
        xrot = x * np.cos(angle) - y * np.sin(angle)
        yrot = x * np.sin(angle) + y * np.cos(angle)

        # Apply translation
        tx, ty = translation
        xrot = xrot + width * tx
        yrot = yrot + height * ty

        # Normalize coordinates
        xrot = np.clip((xrot + width/2) / width, 0, 1)
        yrot = np.clip((yrot + height/2) / height, 0, 1)

        # Sample original values at new coordinates
        coords = np.stack([yrot, xrot], axis=-1)
        return map_coordinates(values, coords.transpose(2, 0, 1), order=1)

    def generate_world_chunk(self, region_x: int, region_y: int) -> dict:
        """Generate a specific region of the world on-demand.
        
        Args:
            region_x: X coordinate of the region
            region_y: Y coordinate of the region
            
        Returns:
            Dictionary containing the generated region data
        """
        # Use a cache key for this specific region
        cache_key = f"region_{region_x}_{region_y}"
        
        # Check if we have a cached version
        if hasattr(self, '_region_cache') and cache_key in self._region_cache:
            return self._region_cache[cache_key]
            
        # Initialize cache if needed
        if not hasattr(self, '_region_cache'):
            self._region_cache = {}
            self._region_cache_order = []
            self.max_cached_regions = 16  # Keep memory usage in check
        
        # Generate the biome data for this region
        region_width = self.region_size
        region_height = self.region_size
        
        # Calculate world position of this region
        start_x = region_x * region_width
        start_y = region_y * region_height
        
        # Generate noise for this region with padding to avoid edge artifacts
        padding = 4
        padded_width = region_width + 2 * padding
        padded_height = region_height + 2 * padding
        
        # Generate the full biome grid for this region
        biome_grid, heightmap, temp_map, rain_map = self.generate_full_biome_grid(
            padded_width, padded_height, 
            self.seed + region_x * 31 + region_y * 17,  # Unique seed per region
            self.biome_data
        )
        
        # Extract just the region we need (without padding)
        tiles = []
        for y in range(padding, padding + region_height):
            row = []
            for x in range(padding, padding + region_width):
                world_x = start_x + (x - padding)
                world_y = start_y + (y - padding)
                
                tile = {
                    'x': world_x,
                    'y': world_y,
                    'biome': biome_grid[y, x],
                    'height': float(heightmap[y, x]),
                    'temperature': float(temp_map[y, x]),
                    'rainfall': float(rain_map[y, x])
                }
                row.append(tile)
            tiles.append(row)
        
        # Create the region data
        region_data = {
            'tiles': tiles,
            'region_x': region_x,
            'region_y': region_y,
            'chunk_size': self.region_size,
            'last_accessed': time.time()
        }
        
        # Add to cache
        self._region_cache[cache_key] = region_data
        self._region_cache_order.append(cache_key)
        
        # Enforce cache size limit
        while len(self._region_cache) > self.max_cached_regions:
            oldest_key = self._region_cache_order.pop(0)
            if oldest_key in self._region_cache:
                del self._region_cache[oldest_key]
        
        return region_data

    def set_starting_biome(self, biome_name: str) -> None:
        """Sets the starting biome for world generation."""
        if biome_name in self.biome_data:
            self.starting_biome = biome_name
            logging.info(f"[TerrainGenerator] Starting biome set to '{biome_name}'")
        else:
            logging.warning(f"[TerrainGenerator] '{biome_name}' is not a valid biome.")

    def validate_biome_colors(self) -> None:
        """Ensure all biomes have associated colors."""
        for biome_name in self.biome_data:
            if biome_name not in self.color_palette:
                logging.warning(f"[TerrainGenerator] Biome '{biome_name}' missing color; using fallback.")
                biome_info = self.biome_data[biome_name]
                if 'color' in biome_info:
                    self.color_palette[biome_name] = biome_info['color']
                else:
                    self.color_palette[biome_name] = self.fallback_color

    # --- NEW LOGIC FOR WORLD GENERATION BASED ON USER DESIGN ---
    def generate_diagonal_heightmap(self, width, height, seed):
        """
        Generate heightmap with multiple landmasses and varied terrain features.
        """
        np.random.seed(seed)

        # Generate base noise with multiple octaves for more natural terrain
        base_noise = generate_perlin_noise(width, height, 50.0, seed)
        detail_noise = generate_perlin_noise(width, height, 25.0, seed + 1)
        fine_noise = generate_perlin_noise(width, height, 10.0, seed + 2)

        # Combine noise layers with different weights
        noise = (0.6 * base_noise + 0.3 * detail_noise + 0.1 * fine_noise)

        # Create continent mask for more realistic landmasses
        # This creates larger continuous land areas
        continent_mask = np.random.rand(height, width)
        continent_mask = gaussian_filter(continent_mask, sigma=10.0)
        continent_mask = (continent_mask > 0.4).astype(float)  # Binary mask for continents

        # Add some mountain ranges
        mountain_noise = generate_perlin_noise(width, height, 30.0, seed + 3)
        mountain_mask = (mountain_noise > 0.7).astype(float)  # Only highest areas become mountains

        # Combine everything
        heightmap = 0.4 * noise + 0.4 * continent_mask + 0.2 * mountain_mask

        # Normalize to 0-1 range
        heightmap = (heightmap - heightmap.min()) / (heightmap.max() - heightmap.min())

        # Apply a slight curve to increase mid-range elevations (more land area)
        heightmap = np.power(heightmap, 0.8)

        return heightmap


    def generate_custom_temperature_map(self, width, height, seed, heightmap):
        """
        Temperature radiates from one of the highest corners (chosen by seed).
        """
        np.random.seed(seed)
        corners = [(0,0), (0,width-1), (height-1,0), (height-1,width-1)]
        corner_idx = seed % 4
        corner_y, corner_x = corners[corner_idx]
        y, x = np.ogrid[0:height, 0:width]
        dist = np.sqrt((x-corner_x)**2 + (y-corner_y)**2)
        max_dist = np.sqrt((width-1)**2 + (height-1)**2)
        temp_map = 1 - (dist / max_dist)
        temp_map = (temp_map * 0.7 + 0.3 * heightmap)  # Blend with elevation
        temp_map += 0.1 * generate_perlin_noise(width, height, 40.0, seed+1)
        temp_map = (temp_map - temp_map.min()) / (temp_map.max() - temp_map.min())
        temp_map = temp_map * 90 - 40  # -40 to 50C
        return temp_map


    def generate_custom_humidity_map(self, width, height, seed, temp_map):
        """
        Humidity is horizontal or vertical (chosen by seed), driest side touches hottest corner.
        """
        np.random.seed(seed)
        # Determine orientation
        orientation = 'horizontal' if (seed % 2 == 0) else 'vertical'
        # Find hottest corner
        corners = [(0,0), (0,width-1), (height-1,0), (height-1,width-1)]
        corner_idx = seed % 4
        if orientation == 'horizontal':
            if corner_idx in [0,2]:
                # driest on left
                grad = np.tile(np.linspace(0,1,width), (height,1))
            else:
                # driest on right
                grad = np.tile(np.linspace(1,0,width), (height,1))
        else:
            if corner_idx in [0,1]:
                # driest on top
                grad = np.tile(np.linspace(0,1,height), (width,1)).T
            else:
                # driest on bottom
                grad = np.tile(np.linspace(1,0,height), (width,1)).T
        # Add some noise
        grad += 0.1 * generate_perlin_noise(width, height, 40.0, seed+2)
        grad = (grad - grad.min()) / (grad.max() - grad.min())
        grad = grad * 200  # 0-200 mm
        return grad


    def assign_biome_from_json(self, temp, rain, height, biome_defs):
        """
        Assign biome using thresholds from biomes.json. If multiple match, use tiebreaker.
        If no match, use the classifier fallback instead of returning 'Unknown'.
        """
        matches = []
        for name, b in biome_defs.items():
            th = b.get('thresholds', {})
            t = th.get('temperature', {})
            r = th.get('rainfall', {})
            h = th.get('height', {})
            if t and r and h:
                if t['min'] <= temp <= t['max'] and r['min'] <= rain <= r['max'] and h['min'] <= height <= h['max']:
                    matches.append(name)
        if not matches:
            # Use the biome manager to get the closest biome
            return self.biome_manager.get_biome_by_conditions(temp, rain, height)
        # Tiebreaker: prefer extremes
        extreme_priority = ['Molten Fields', 'Twilight Marsh', 'Golden Sands Desert', 'Frost-Hewn Tundra']
        for e in extreme_priority:
            if e in matches:
                return e
        return matches[0]


    def generate_full_biome_grid(self, width, height, seed, biome_defs):
        heightmap = self.generate_diagonal_heightmap(width, height, seed)
        temp_map = self.generate_custom_temperature_map(width, height, seed, heightmap)
        rain_map = self.generate_custom_humidity_map(width, height, seed, temp_map)
        biome_grid = np.empty((height, width), dtype=object)
        for y in range(height):
            for x in range(width):
                elev = heightmap[y, x]
                # water vs land
                if elev < self.sea_level:
                    biome_grid[y, x] = 'Ocean'
                else:
                    # use biome manager to classify the tile
                    biome_grid[y, x] = self.biome_manager.get_biome_by_conditions(
                        temp_map[y, x], rain_map[y, x], elev)
        return biome_grid, heightmap, temp_map, rain_map

    # --- END NEW LOGIC ---

    def debug_output_biome_grid(self, filename_ascii=None, filename_json=None):
        """
        Output the full biome grid as ASCII art and/or JSON for debug purposes.
        """
        if not hasattr(self, '_full_biome_cache'):
            return
        biome_grid = self._full_biome_cache['biome_grid']
        height, width = biome_grid.shape
        # ASCII output
        if filename_ascii:
            biome_symbols = {
                'Molten Fields': 'M',
                'Twilight Marsh': 'W',
                'Golden Sands Desert': 'D',
                'Frost-Hewn Tundra': 'T',
                'Verdant Forest': 'F',
                'Highland Fields': 'H',
                'Unknown': '?'
            }
            with open(filename_ascii, 'w') as f:
                for y in range(height):
                    row = ''.join(biome_symbols.get(biome_grid[y, x], '.') for x in range(width))
                    f.write(row + '\n')
        # JSON output
        if filename_json:
            import json
            biome_list = [[biome_grid[y, x] for x in range(width)] for y in range(height)]
            with open(filename_json, 'w') as f:
                json.dump(biome_list, f, indent=2)
