"""
World management package for Living World RPG.

This package contains all the modules related to world generation, management,
and simulation, including biomes, terrain, resources, settlements, factions,
economy, and world events.
"""

# Local application imports
from world.biome_manager import BiomeManager
from world.economy_manager import EconomyManager
from world.event_manager import EventManager
from world.faction_manager import FactionManager
from world.resource_manager import ResourceManager
from world.settlement_manager import SettlementManager
from world.terrain_generator import TerrainGenerator
from world.world_history import WorldHistory
from world.world_manager import WorldManager

__all__ = [
    'BiomeManager',
    'EconomyManager',
    'EventManager',
    'FactionManager',
    'ResourceManager',
    'SettlementManager',
    'TerrainGenerator',
    'WorldHistory',
    'WorldManager',
]