"""
Event Manager for Living World RPG

- Manages registration, triggering, and tracking of world events.
- Supports day-based triggers (e.g., "on_day_10") and executes associated handlers.
"""

# Standard library imports
import logging
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
# (none in this file)

# Configure logging
logging.basicConfig(level=logging.INFO)

class Event:
    def __init__(self, name: str, trigger: str, handler: Optional[Callable] = None):
        self.name = name
        self.trigger = trigger  # Expected format: "on_day_X"
        self.handler = handler
        self.is_triggered = False

    def __repr__(self):
        return f"Event(name={self.name}, trigger={self.trigger}, triggered={self.is_triggered})"

class EventManager:
    def __init__(self, event_thresholds: dict = None):
        self.events: List[Event] = []
        self.events_by_day: Dict[int, List[Event]] = {}
        self.triggered_events: Set[str] = set()
        self.event_thresholds = event_thresholds or {}
        logging.info("[EventManager] Initialized with %d event thresholds.", len(self.event_thresholds))

    def register_event(self, event_name: str, trigger: str, handler: Optional[Callable] = None) -> None:
        day = self._parse_trigger(trigger)
        if day is None:
            logging.error(f"[EventManager] Invalid trigger '{trigger}' for event '{event_name}'.")
            raise ValueError(f"Invalid trigger format: '{trigger}'.")
        event = Event(name=event_name, trigger=trigger, handler=handler)
        self.events.append(event)
        self.events_by_day.setdefault(day, []).append(event)
        logging.info(f"[EventManager] Registered event '{event_name}' with trigger '{trigger}'.")

    def advance_day(self, current_day: int) -> None:
        events_today = self.events_by_day.get(current_day, [])
        if not events_today:
            logging.debug(f"[EventManager] No events scheduled for day {current_day}.")
            return
        for event in events_today:
            if event.name in self.triggered_events:
                continue
            self.trigger_event(event)
            self.triggered_events.add(event.name)

    def trigger_event(self, event: Event) -> None:
        logging.info(f"[EventManager] Triggering event '{event.name}' (trigger: {event.trigger}).")
        if event.handler:
            try:
                event.handler()
                logging.info(f"[EventManager] Event '{event.name}' handler executed.")
            except Exception as e:
                logging.error(f"[EventManager] Error executing event '{event.name}': {e}")
        else:
            logging.info(f"[EventManager] No handler for event '{event.name}'.")

    def _parse_trigger(self, trigger: str) -> Optional[int]:
        if not trigger.startswith("on_day_"):
            return None
        try:
            return int(trigger.split("on_day_")[1])
        except (IndexError, ValueError):
            return None

    def unregister_event(self, event_name: str) -> None:
        event = next((e for e in self.events if e.name == event_name), None)
        if not event:
            logging.error(f"[EventManager] Event '{event_name}' not found.")
            raise ValueError(f"Event '{event_name}' not found.")
        self.events.remove(event)
        day = self._parse_trigger(event.trigger)
        if day is not None and day in self.events_by_day:
            self.events_by_day[day].remove(event)
            if not self.events_by_day[day]:
                del self.events_by_day[day]
        logging.info(f"[EventManager] Unregistered event '{event_name}'.")

    def clear_all_events(self) -> None:
        self.events.clear()
        self.events_by_day.clear()
        self.triggered_events.clear()
        logging.info("[EventManager] Cleared all events.")

    def get_upcoming_events(self, from_day: int = 1) -> List[Event]:
        upcoming = []
        for day in sorted(self.events_by_day.keys()):
            if day >= from_day:
                upcoming.extend(self.events_by_day[day])
        return upcoming

    def get_all_events(self) -> List[Event]:
        return self.events.copy()

    def has_event_been_triggered(self, event_name: str) -> bool:
        return event_name in self.triggered_events
