"""
Economy Manager for Living World RPG

- Calculates dynamic prices based on supply and demand in settlements.
- Integrates historical events (placeholder) to adjust market conditions.
"""

# Standard library imports
import logging
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
from core.components import FactionComponent, InventoryComponent, SettlementComponent
from utils.elastic_dict_adapter import ElasticDictAdapter

# Configure logging
logging.basicConfig(level=logging.INFO)

class EconomyManager:
    def __init__(self, world, items_data, faction_manager, history_manager=None):
        self.world = world
        self.items_data = items_data
        self.faction_manager = faction_manager
        self.history_manager = history_manager
        # Base prices for each item from items_data
        self.base_prices = ElasticDictAdapter({item: data.get("base_price", 10) for item, data in items_data.items()}, size=128)
        self.dynamic_prices = ElasticDictAdapter(self.base_prices.to_dict(), size=128)

    def calculate_prices(self, settlements):
        supply = ElasticDictAdapter(size=128)
        demand = ElasticDictAdapter(size=128)
        # Aggregate supply from settlement inventories
        for entity, (settlement, inventory) in settlements:
            for item, qty in inventory.items.items():
                supply[item] = supply.get(item, 0) + qty
            # Increase demand for preferred goods (if available)
            faction_comp = entity.get_component(FactionComponent)
            faction_name = faction_comp.faction_name if faction_comp else None
            preferred = self.get_preferred_goods(faction_name)
            for p_item in preferred:
                demand[p_item] = demand.get(p_item, 0) + 10
            # Base demand for all items
            for item in self.base_prices:
                demand[item] = demand.get(item, 0) + 5

        # Placeholder: adjust demand based on history if available
        if self.history_manager and hasattr(self.history_manager, 'get_recent_events'):
            recent_events = self.history_manager.get_recent_events()
            self.incorporate_history(recent_events, supply, demand)

        self.update_dynamic_prices(supply, demand)

    def update_dynamic_prices(self, supply: dict, demand: dict):
        for item, base_price in self.base_prices.items():
            s = supply.get(item, 0)
            d = demand.get(item, 1)
            ratio = d / (s if s > 0 else 1)
            new_price = max(1, int(base_price * ratio))
            self.dynamic_prices[item] = new_price
        logging.debug(f"[EconomyManager] Updated dynamic prices: {self.dynamic_prices}")

    def incorporate_history(self, history_events, supply, demand):
        # Future implementation: adjust supply/demand based on historical events
        pass

    def get_price(self, item: str) -> int:
        return self.dynamic_prices.get(item, self.base_prices.get(item, 10))

    def get_preferred_goods(self, faction_name: str) -> list:
        if not faction_name:
            return []
        faction_info = self.faction_manager.get_faction_info(faction_name)
        if not faction_info:
            logging.warning(f"[EconomyManager] No info for faction '{faction_name}'.")
            return []
        return faction_info.get("preferred_goods", [])
