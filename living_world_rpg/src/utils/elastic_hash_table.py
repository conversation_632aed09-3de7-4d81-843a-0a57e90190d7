"""
ElasticHashTable for Living World RPG

A high-performance hash table implementation using Funnel Hashing, optimized for game development.
Provides O(log² 1/δ) worst-case expected probes without relocating keys after insertion.
"""

# Standard library imports
import array
import math
from typing import Any, Dict, List, Optional, Tuple, TypeVar, Union

# Third-party imports
# (none in this file)

# Local application imports
# (none in this file)

class ElasticHashTable:
    """High-performance hash table optimized for game development.

    This implementation uses Funnel Hashing, which provides O(log² 1/δ) worst-case
    expected probes without ever relocating keys after insertion.
    """
    def __init__(self, size: int = 128, delta: float = 0.25):
        # Ensure size is a power of 2 for bit masking instead of modulo
        self.size = self._next_power_of_2(size)
        self.mask = self.size - 1  # Bit mask for fast modulo
        self.delta = delta        # Slack parameter (lower = more memory efficient)
        self.load_factor = 1 - delta  # Maximum load factor before resizing
        self.count = 0
        self.deleted_count = 0

        # Use array module for more efficient memory usage
        # Store hash values for faster comparisons (avoid recalculating hash)
        self.hashes = array.array('L', [0] * self.size)
        self.keys = [None] * self.size
        self.values = [None] * self.size

        # Special marker for deleted entries
        self.DELETED = object()

        # Cache hash of DELETED for faster comparisons
        self.DELETED_HASH = 0

        # Number of hash functions to use (L = ⌈log₂ 1/δ⌉)
        self.L = max(1, math.ceil(math.log2(1/delta)))

    def _next_power_of_2(self, n):
        """Return the next power of 2 greater than or equal to n."""
        return 1 if n == 0 else 2**(n - 1).bit_length()

    def _hash_key(self, key):
        """Compute hash value for a key, ensuring it's never 0."""
        # Use Python's built-in hash function
        h = hash(key) & 0x7FFFFFFF  # Ensure positive hash
        return h if h != 0 else 1  # Ensure non-zero (0 reserved for empty slots)

    def _candidate_indices(self, key):
        """Generate L candidate indices for the key using independent hash functions."""
        # Special handling for CollisionKey objects used in benchmarks
        if hasattr(key, '__hash__') and hasattr(key, 'value') and hasattr(key, 'hash_value'):
            # For CollisionKey objects, use the value to differentiate indices
            # This prevents infinite loops in collision-heavy scenarios
            base_hash = key.hash_value
            return [(base_hash + i * key.value) & self.mask for i in range(self.L)]
        else:
            # Use a combination of the key and index i to create L independent hash functions
            return [(hash((key, i)) & self.mask) for i in range(self.L)]

    def insert(self, key, value):
        """Insert or update a key-value pair in the table."""
        # Check load factor before computing hash (optimization)
        if self.count + self.deleted_count >= int(self.size * self.load_factor):
            self._resize(self.size * 2)  # Double the size

        # Compute hash once for faster comparisons
        hash_val = self._hash_key(key)

        # Generate L candidate indices for this key
        indices = self._candidate_indices(key)

        # First pass: check if the key already exists in any of the candidate positions
        for idx in indices:
            # Fast path: check hash before comparing keys
            if self.hashes[idx] == hash_val and self.keys[idx] == key:
                # Key exists, update value
                self.values[idx] = value
                return

        # Second pass: find the first available slot (empty or deleted)
        first_deleted = -1
        for idx in indices:
            # Remember the first deleted slot we find
            if first_deleted == -1 and self.keys[idx] is self.DELETED:
                first_deleted = idx
                continue

            # Empty slot found
            if self.hashes[idx] == 0:
                # If we found a deleted slot earlier, use that instead
                if first_deleted != -1:
                    idx = first_deleted
                    self.deleted_count -= 1  # We're reusing a deleted slot

                # Insert new entry
                self.hashes[idx] = hash_val
                self.keys[idx] = key
                self.values[idx] = value
                self.count += 1
                return

        # If we get here, all candidate slots are occupied by other keys
        # Use the first deleted slot if we found one
        if first_deleted != -1:
            idx = first_deleted
            self.hashes[idx] = hash_val
            self.keys[idx] = key
            self.values[idx] = value
            self.count += 1
            self.deleted_count -= 1  # We're reusing a deleted slot
            return

        # If we get here, all candidate slots are occupied and none are deleted
        # This should be rare with proper sizing, but we need to handle it
        self._resize(self.size * 2)
        self.insert(key, value)  # Try again with the larger table

    def get(self, key):
        """Get the value associated with the key, or None if not found."""
        # Compute hash once for faster comparisons
        hash_val = self._hash_key(key)

        # Generate L candidate indices for this key
        indices = self._candidate_indices(key)

        # Check all candidate positions
        for idx in indices:
            # Fast path: check hash before comparing keys
            if self.hashes[idx] == hash_val and self.keys[idx] == key:
                return self.values[idx]

            # If we hit an empty slot, the key is definitely not in the table
            # (this optimization only works if we never move keys after insertion)
            if self.hashes[idx] == 0:
                return None

        # If we checked all candidate positions and didn't find the key,
        # it's not in the table
        return None

    def remove(self, key):
        """Remove a key-value pair from the table."""
        # Compute hash once for faster comparisons
        hash_val = self._hash_key(key)

        # Generate L candidate indices for this key
        indices = self._candidate_indices(key)

        # Check all candidate positions
        for idx in indices:
            # Fast path: check hash before comparing keys
            if self.hashes[idx] == hash_val and self.keys[idx] == key:
                # Mark as deleted
                self.hashes[idx] = self.DELETED_HASH
                self.keys[idx] = self.DELETED
                self.values[idx] = None
                self.count -= 1
                self.deleted_count += 1

                # If too many deleted entries, resize to clean up
                if self.deleted_count > self.size // 4:
                    self._resize(self.size)  # Same size, just clean up
                return

            # If we hit an empty slot, the key is definitely not in the table
            if self.hashes[idx] == 0:
                return

    def _resize(self, new_size=None):
        """Resize the hash table to accommodate more entries or clean up deleted entries."""
        if new_size is None:
            new_size = self.size * 2

        # Save old data
        old_size = self.size
        old_keys = self.keys
        old_values = self.values

        # Initialize new table
        self.size = self._next_power_of_2(new_size)
        self.mask = self.size - 1
        self.hashes = array.array('L', [0] * self.size)
        self.keys = [None] * self.size
        self.values = [None] * self.size
        self.count = 0
        self.deleted_count = 0

        # Update the number of hash functions based on the new size
        # L = ⌈log₂ 1/δ⌉
        self.L = max(1, math.ceil(math.log2(1/self.delta)))

        # Re-insert all non-deleted entries
        for i in range(old_size):
            if old_keys[i] is not None and old_keys[i] is not self.DELETED:
                # Get the key and value
                key = old_keys[i]
                value = old_values[i]

                # Insert using the new set of hash functions
                # We use direct insertion here to avoid potential recursion

                # Compute hash once for faster comparisons
                hash_val = self._hash_key(key)

                # Generate L candidate indices for this key
                indices = self._candidate_indices(key)

                # Try each candidate position
                inserted = False
                for idx in indices:
                    if self.hashes[idx] == 0:  # Empty slot
                        # Insert directly
                        self.hashes[idx] = hash_val
                        self.keys[idx] = key
                        self.values[idx] = value
                        self.count += 1
                        inserted = True
                        break

                # If all candidate positions are full, find any empty slot
                # This should be rare since we just resized the table
                if not inserted:
                    for idx in range(self.size):
                        if self.hashes[idx] == 0:  # Empty slot
                            # Insert directly
                            self.hashes[idx] = hash_val
                            self.keys[idx] = key
                            self.values[idx] = value
                            self.count += 1
                            break

    def items(self):
        """Return a list of (key, value) tuples for all items in the table."""
        result = []
        for i in range(self.size):
            if self.keys[i] is not None and self.keys[i] is not self.DELETED:
                result.append((self.keys[i], self.values[i]))
        return result

    def get_keys(self):
        """Return a list of all keys in the table."""
        return [k for k in self.keys if k is not None and k is not self.DELETED]

    def get_values(self):
        """Return a list of all values in the table."""
        result = []
        for i in range(self.size):
            if self.keys[i] is not None and self.keys[i] is not self.DELETED:
                result.append(self.values[i])
        return result

    def __contains__(self, key):
        """Check if key exists in the table."""
        return self.get(key) is not None

    def __len__(self):
        """Return the number of items in the table."""
        return self.count

    def to_dict(self):
        """Convert to a regular Python dictionary."""
        return dict(self.items())
